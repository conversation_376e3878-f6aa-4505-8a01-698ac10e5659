# Import built-in modules
from functools import partial
import logging

# Import third-party modules
import maya.cmds as cmds
import maya.mel as mel

# Import local modules
import cgame_avatar_factory.hair_studio.maya_api.global_deformer.core_fun as core_fun
import cgame_avatar_factory.hair_studio.maya_api.global_deformer.gd_maya_api as gd_maya_api

MENU_PREFIX = "[GWrap变形器] "

# 默认菜单配置示例
DEFAULT_MENU_CONFIG = [
    {
        "label": MENU_PREFIX + "生成曲线",
        "selection_type": "mesh",  # 选择类型：transform, mesh, nurbsCurve等
        "component_type": None,  # 组件类型：face, vertex, edge 或 None
        "selected_obj_num": 1,
        "command": lambda: gd_maya_api.create_curve_on_menu(),
    },
    {
        "label": MENU_PREFIX + "生成变形器",
        "selection_type": "nurbsCurve",
        "component_type": None,
        "selected_obj_num": 2,
        "command": lambda: gd_maya_api.create_deformer_on_menu(),
    },
    {
        "label": MENU_PREFIX + "调整曲线受影响面",
        "selection_type": "nurbsCurve",  # 选择类型：transform, mesh, nurbsCurve等
        "component_type": None,  # 组件类型：face, vertex, edge 或 None
        "selected_obj_num": 1,
        "command": lambda: gd_maya_api.adjust_curve_deformer_weight(),
    },
]


class AllViewportObjectMenuManager:
    """
    给所有打开的 modelPanel 绑定右键菜单回调，
    根据配置信息动态添加自定义菜单项。
    支持绑定和解绑，完善的菜单项生命周期管理。
    """

    CUSTOM_MENU_TAG = "MY_CUSTOM_MENU_ITEM"
    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, menu_config=None):
        self.logger = logging.getLogger(__name__)

        if self._initialized:
            return
        self._initialized = True
        # 记录已绑定的菜单名列表，方便解绑
        self.bound_menus = set()
        # 记录已创建的菜单项，用于生命周期管理 {menu_name: [item1, item2, ...]}
        self.created_menu_items = {}
        # 保存原始的postMenuCommand，用于恢复 {menu_name: original_command}
        self.original_menu_commands = {}
        # 菜单配置信息
        self.menu_config = menu_config or DEFAULT_MENU_CONFIG

        self.middle_ui_event_manager = None

        self.logger.info("[初始化] 菜单管理器已创建，读取了 {} 个菜单项".format(len(self.menu_config)))

    def clear_custom_menu_items(self, menuName):
        """清理指定菜单的自定义菜单项"""
        if menuName in self.created_menu_items:
            for item in self.created_menu_items[menuName]:
                try:
                    if cmds.menuItem(item, query=True, exists=True):
                        self.logger.debug("[清理] 删除菜单项: {}".format(item))
                        cmds.deleteUI(item, menuItem=True)
                except Exception as e:
                    self.logger.error("[清理] 删除菜单项失败: {}, 错误: {}".format(item, e))

            # 清空记录
            self.created_menu_items[menuName] = []

    def _enhanced_menu_callback(self, menuName, original_cmd, *args):
        """
        增强的菜单回调函数，先执行原始命令，再添加自定义菜单项
        """
        self.logger.debug("[增强回调] 菜单: {}, 原始命令: {}".format(menuName, original_cmd))

        # 先执行原始的菜单命令（如果存在）
        self.logger.debug("[增强回调] 执行原始菜单命令: {0}， is strip: {1}".format(original_cmd, original_cmd.strip()))
        if original_cmd:
            try:
                # 如果是字符串命令，直接执行
                if isinstance(original_cmd, str):
                    self.logger.debug("[增强回调] 执行原始MEL命令: {}".format(original_cmd))
                    mel.eval(original_cmd)
                # 如果是函数对象，调用它
                elif callable(original_cmd):
                    self.logger.debug("[增强回调] 执行原始Python回调")
                    original_cmd(*args)
            except Exception as e:
                self.logger.error("[增强回调] 执行原始命令失败: {}".format(e))

        # 然后添加我们的自定义菜单项
        self._menu_callback(menuName, *args)

    def _menu_callback(self, menuName, *args):
        """菜单回调函数，根据配置动态添加菜单项"""
        self.logger.debug("[菜单回调] 弹出菜单: {}".format(menuName))
        self.clear_custom_menu_items(menuName)

        # 获取所有选择信息
        all_sels, sel_count = core_fun.resolve_selection_type(True)
        if sel_count == 0:
            self.logger.debug("[菜单回调] 未选中任何对象，不添加自定义菜单")
            return

        self.logger.debug("[菜单回调] 选中数量: {} -> {}".format(sel_count, all_sels))

        # 初始化菜单项列表
        if menuName not in self.created_menu_items:
            self.created_menu_items[menuName] = []

        # 根据配置添加菜单项
        added_count = 0
        for config in self.menu_config:
            sel_type = config.get("selection_type")
            comp_type_needed = config.get("component_type")
            required_count = config.get("selected_obj_num")

            # 要求：在当前选择集中，至少存在一个对象匹配 selection_type / component_type
            has_match = False
            for so, ot, ct in all_sels:
                if ot != sel_type:
                    continue
                if comp_type_needed is not None and ct != comp_type_needed:
                    continue
                has_match = True
                break
            if not has_match:
                continue

            # 校验所需的选中数量
            if required_count is not None and sel_count != required_count:
                continue

            try:
                cmds.menuItem(parent=menuName, divider=True)
                added_item = cmds.menuItem(
                    label=config["label"],
                    parent=menuName,
                    annotation=self.CUSTOM_MENU_TAG,
                    command=partial(self._execute_command, config["command"]),
                )

                # 记录创建的菜单项
                self.created_menu_items[menuName].append(added_item)
                added_count += 1
                self.logger.debug("[菜单回调] 添加菜单项: {} -> {}".format(config["label"], added_item))

            except Exception as e:
                self.logger.error("[菜单回调] 添加菜单项失败: {}, 错误: {}".format(config["label"], e))

        if added_count == 0:
            self.logger.debug("[菜单回调] 没有匹配的菜单项可添加")
        else:
            self.logger.info("[菜单回调] 共添加了 {} 个菜单项".format(added_count))

    def _execute_command(self, command_func, *args):
        """执行菜单命令"""
        try:
            self.logger.debug("[命令执行] 执行菜单命令，选择列表: {}".format(args))
            try:
                # 优先按带参数方式调用（传入选择列表）
                command_func(args)
            except TypeError:
                # 兼容无参命令
                command_func()
        except Exception as e:
            self.logger.error("[命令执行] 执行失败: {}".format(e))
            cmds.inViewMessage(amg="命令执行失败: " + str(e), pos="topCenter", fade=True)

    def bind(self, middle_ui_event_manager=None):
        """
        遍历所有modelPanel，绑定它们的ObjectPop菜单回调。
        绑定前先解绑之前绑定的菜单，避免重复。
        """
        self.logger.info("[绑定] 开始绑定所有modelPanel的ObjectPop菜单")

        # 先解绑
        self.unbind()
        self.middle_ui_event_manager = middle_ui_event_manager

        panels = cmds.getPanel(type="modelPanel")
        if not panels:
            self.logger.warning("[绑定] 未找到任何modelPanel，绑定失败")
            return False

        count = 0
        for panel in panels:
            menuName = panel + "ObjectPop"
            # 检查菜单是否存在
            if cmds.popupMenu(menuName, exists=True):
                try:
                    # 使用 cmds.menu 查询 postMenuCommand（参考 dagmenu.py）
                    menu_cmd = cmds.menu(menuName, query=True, postMenuCommand=True) or ""
                    self.original_menu_commands[menuName] = menu_cmd
                    self.logger.debug("[绑定] 保存原始菜单命令：{} -> {}".format(menuName, menu_cmd))
                    if menu_cmd:
                        # Maya's dag menu post command has the parent menu in it
                        parent_menu = menu_cmd.split(" ")[-1]
                        self.logger.debug("[绑定] 父菜单：{}, this menu name: {}".format(parent_menu, menuName))

                        # 设置新的回调，使用 cmds.menu 设置（参考 dagmenu.py）
                        new_callback = partial(self._enhanced_menu_callback, menuName, menu_cmd)
                        cmds.menu(menuName, edit=True, postMenuCommand=new_callback)

                    self.bound_menus.add(menuName)
                    count += 1
                    self.logger.debug("[绑定] 成功绑定菜单回调：{}".format(menuName))
                except Exception as e:
                    self.logger.error("[绑定] 绑定菜单 {} 时出错：{}".format(menuName, e))
            else:
                self.logger.warning("[绑定] 菜单不存在，跳过：{}".format(menuName))

        self.logger.info("[绑定] 完成，绑定了 {} 个菜单".format(count))
        return True

    def unbind(self):
        """
        解绑之前绑定的所有菜单回调，恢复原始的postMenuCommand，并清理自定义菜单项
        """
        self.middle_ui_event_manager = None

        if not self.bound_menus:
            self.logger.debug("[解绑] 当前无绑定菜单，跳过")
            return

        for menuName in list(self.bound_menus):
            if cmds.popupMenu(menuName, exists=True):
                try:
                    # 恢复原始的postMenuCommand，使用 MEL 命令（参考 dagmenu.py）
                    original_cmd = self.original_menu_commands.get(menuName, "")
                    if original_cmd and "buildObjectMenuItemsNow" in original_cmd:
                        self.logger.debug("[解绑] 恢复原始菜单命令：{} -> {}".format(menuName, original_cmd))
                        # 从原始命令中提取 parent_menu 路径
                        # 格式: "buildObjectMenuItemsNow MainPane|viewPanes|modelPanel4|modelPanel4|modelPanel4|modelPanel4ObjectPop"
                        parent_menu = original_cmd.split(" ")[-1]

                        # 使用正确的 MEL 命令格式恢复原始的 postMenuCommand
                        # 格式: menu -edit -postMenuCommand "buildObjectMenuItemsNow parent_menu" menuName
                        mel.eval(
                            'menu -edit -postMenuCommand "buildObjectMenuItemsNow '
                            + parent_menu.replace('"', "")
                            + '" '
                            + menuName,
                        )
                    else:
                        self.logger.debug("[解绑] 清空菜单命令：{}".format(menuName))
                        # 清空 postMenuCommand
                        mel.eval(
                            'menu -edit -postMenuCommand "" ' + menuName,
                        )

                    self.logger.debug("[解绑] 成功解绑菜单回调：{}".format(menuName))
                except Exception as e:
                    self.logger.error("[解绑] 解绑菜单 {} 时出错：{}".format(menuName, e))

                self.clear_custom_menu_items(menuName)
            else:
                self.logger.warning("[解绑] 菜单不存在，跳过解绑：{}".format(menuName))

            self.bound_menus.remove(menuName)

        # 清空所有记录
        self.created_menu_items.clear()
        self.original_menu_commands.clear()
        self.logger.info("[解绑] 完成解绑所有菜单，恢复了原始菜单命令，清理了所有记录")

    def add_menu_config(self, label, selection_type, component_type=None, command=None):
        """动态添加菜单配置"""
        if command is None:
            command = lambda obj: cmds.inViewMessage(amg="执行了菜单: " + label, pos="topCenter", fade=True)

        config = {
            "label": label,
            "selection_type": selection_type,
            "component_type": component_type,
            "command": command,
        }

        self.menu_config.append(config)
        self.logger.info("[配置] 添加菜单配置: {}".format(label))

    def remove_menu_config(self, label):
        """移除菜单配置"""
        self.menu_config = [cfg for cfg in self.menu_config if cfg["label"] != label]
        self.logger.info("[配置] 移除菜单配置: {}".format(label))

    def __del__(self):
        """析构函数，确保实例销毁时清理所有菜单"""
        try:
            self.logger.info("[析构] 菜单管理器实例即将销毁，开始清理...")
            self.cleanup()
            self.logger.info("[析构] 菜单管理器清理完成")
        except Exception as e:
            self.logger.error("[析构] 清理过程中出错: {}".format(e))

    def cleanup(self):
        """清理所有资源"""
        self.unbind()
