"""Asset Configuration for Hair Studio.

This module provides configuration settings for asset paths and file extensions.
Users can modify these settings to point to their own asset directories.
"""

# Import built-in modules
import logging
import os

# Import local modules
# Import constants from hair_studio constants module
from cgame_avatar_factory.hair_studio.constants import ENV_CARD_PATH
from cgame_avatar_factory.hair_studio.constants import ENV_CURVE_PATH
from cgame_avatar_factory.hair_studio.constants import ENV_XGEN_PATH
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CARD
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CURVE
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_XGEN

# Import new asset library configuration
from cgame_avatar_factory.hair_studio.ui.asset_library.asset_lib_config import get_asset_paths_by_type


def get_asset_paths():
    """Get asset paths from environment variables or dynamic discovery.

    Returns:
        dict: Dictionary mapping asset types to their path lists
    """

    def _parse_paths(env_value, default_paths):
        """Parse environment variable value into path list."""
        if env_value:
            # Split by semicolon or comma for multiple paths
            paths = [p.strip() for p in env_value.replace(";", ",").split(",") if p.strip()]
            return paths if paths else default_paths
        return default_paths

    # Get paths from new dynamic discovery system for each asset type
    card_default_paths = get_asset_paths_by_type(HAIR_TYPE_CARD)
    xgen_default_paths = get_asset_paths_by_type(HAIR_TYPE_XGEN)
    curve_default_paths = get_asset_paths_by_type(HAIR_TYPE_CURVE)

    return {
        HAIR_TYPE_CARD: _parse_paths(os.environ.get(ENV_CARD_PATH), card_default_paths),
        HAIR_TYPE_XGEN: _parse_paths(os.environ.get(ENV_XGEN_PATH), xgen_default_paths),
        HAIR_TYPE_CURVE: _parse_paths(os.environ.get(ENV_CURVE_PATH), curve_default_paths),
    }


def set_asset_path(asset_type, path):
    """Set asset path for a specific type.

    Args:
        asset_type (str): Type of asset (card, xgen, curve)
        path (str or list): Path to the asset directory, or list of paths
    """
    env_vars = {
        HAIR_TYPE_CARD: ENV_CARD_PATH,
        HAIR_TYPE_XGEN: ENV_XGEN_PATH,
        HAIR_TYPE_CURVE: ENV_CURVE_PATH,
    }

    if asset_type in env_vars:
        if isinstance(path, list):
            path_str = ",".join(path)
        else:
            path_str = str(path)
        os.environ[env_vars[asset_type]] = path_str
    else:
        raise ValueError(f"Unknown asset type: {asset_type}")


def set_asset_paths(asset_type, paths):
    """Set multiple asset paths for a specific type.

    Args:
        asset_type (str): Type of asset (card, xgen, curve)
        paths (list): List of paths to asset directories
    """
    set_asset_path(asset_type, paths)


def get_asset_path(asset_type):
    """Get asset path for a specific type (backward compatibility).

    Args:
        asset_type (str): Type of asset (card, xgen, curve)

    Returns:
        str: First path in the asset directory list (for backward compatibility)
    """
    paths = get_asset_paths()
    path_list = paths.get(asset_type, [])
    return path_list[0] if path_list else None


def get_asset_path_list(asset_type):
    """Get all asset paths for a specific type.

    Args:
        asset_type (str): Type of asset (card, xgen, curve)

    Returns:
        list: List of paths to asset directories
    """
    paths = get_asset_paths()
    return paths.get(asset_type, [])


def validate_asset_paths():
    """Validate that all configured asset paths exist.

    Returns:
        dict: Dictionary with asset types as keys and validation results as values
              Each value is a dict with 'valid_paths', 'invalid_paths', and 'all_valid' keys
    """
    paths = get_asset_paths()
    result = {}

    for asset_type, path_list in paths.items():
        valid_paths = []
        invalid_paths = []

        for path in path_list:
            if path and os.path.exists(path):
                valid_paths.append(path)
            else:
                invalid_paths.append(path)

        result[asset_type] = {
            "valid_paths": valid_paths,
            "invalid_paths": invalid_paths,
            "all_valid": len(invalid_paths) == 0,
        }

    return result


def print_asset_configuration():
    """Print current asset configuration for debugging."""
    logger = logging.getLogger(__name__)
    logger.info("Hair Studio Asset Configuration:")

    paths = get_asset_paths()
    validation = validate_asset_paths()

    for asset_type, path_list in paths.items():
        validation_info = validation[asset_type]
        logger.info(f"{asset_type.upper()} ({len(path_list)} paths):")

        for path in validation_info["valid_paths"]:
            logger.info(f"  ✓ {path}")

        for path in validation_info["invalid_paths"]:
            logger.info(f"  ✗ {path}")

        if not path_list:
            logger.info(f"  (No paths configured)")

    logger.debug("\nEnvironment Variables:")
    logger.debug(f"  {ENV_CARD_PATH}: {os.environ.get(ENV_CARD_PATH, 'Not set')}")
    logger.debug(f"  {ENV_XGEN_PATH}: {os.environ.get(ENV_XGEN_PATH, 'Not set')}")
    logger.debug(f"  {ENV_CURVE_PATH}: {os.environ.get(ENV_CURVE_PATH, 'Not set')}")
