"""Hair Studio Data Models.

This module defines the core data models used throughout the Hair Studio tool,
including assets, components, and projects.
"""

# Import built-in modules
# Import standard library
import uuid


class HairAsset(object):
    """Represents a hair asset that can be used to create hair components.

    Attributes:
        id (str): Unique identifier for the asset
        name (str): Display name of the asset
        type (str): Type of the asset (e.g., 'card', 'xgen', 'curve')
        sub_asset_type (str or None): Sub-type of the asset (e.g., 'scalp', 'hair')
        thumbnail (str or None): Path to the thumbnail image, or None
        metadata (dict): Additional metadata about the asset
    """

    def __init__(
        self,
        id=None,
        name="Unnamed Asset",
        asset_type="card",
        sub_asset_type=None,
        thumbnail=None,
        metadata=None,
    ):
        """Initialize a new HairAsset.

        Args:
            id (str, optional): Unique identifier. If None, a UUID will be generated.
            name (str, optional): Display name of the asset.
            asset_type (str, optional): Type of the asset. Defaults to 'card'.
            sub_asset_type (str, optional): Sub-type of the asset (e.g., 'scalp', 'hair').
            thumbnail (str, optional): Path to the thumbnail image.
            metadata (dict, optional): Additional metadata.
        """
        self.id = id if id is not None else str(uuid.uuid4())
        self.name = name
        self.type = asset_type
        self.sub_asset_type = sub_asset_type
        self.thumbnail = thumbnail
        self.metadata = metadata or {}

    def to_dict(self):
        """Convert the asset to a dictionary.

        Returns:
            dict: Dictionary representation of the asset
        """
        return {
            "id": self.id,
            "name": self.name,
            "type": self.type,
            "asset_type": self.type,  # Add alias for compatibility
            "sub_asset_type": self.sub_asset_type,
            "thumbnail": self.thumbnail,
            "metadata": self.metadata,
        }

    @classmethod
    def from_dict(cls, data):
        """Create a HairAsset from a dictionary.

        Args:
            data (dict): Dictionary containing asset data

        Returns:
            HairAsset: A new HairAsset instance
        """
        return cls(
            id=data.get("id"),
            name=data.get("name", "Unnamed Asset"),
            asset_type=data.get("type", "card"),
            sub_asset_type=data.get("sub_asset_type"),
            thumbnail=data.get("thumbnail"),
            metadata=data.get("metadata", {}),
        )


class HairComponent(object):
    """Represents an instance of a hair asset in the scene.

    Attributes:
        id (str): Unique identifier for the component
        asset_id (str): ID of the asset this component is based on
        name (str): Display name of the component
        type (str): Type of the component (e.g., 'card', 'xgen', 'curve')
        parameters (dict): Component-specific parameters
        transform (dict): Transform data for the component
        metadata (dict): Additional metadata about the component
    """

    def __init__(
        self,
        id=None,
        asset_id=None,
        name="Unnamed Component",
        component_type="card",
        parameters=None,
        transform=None,
        metadata=None,
        node_names={},
        is_viewed=True,
    ):
        """Initialize a new HairComponent.

        Args:
            id (str, optional): Unique identifier. If None, a UUID will be generated.
            asset_id (str, optional): ID of the asset this component is based on.
            name (str, optional): Display name of the component.
            component_type (str, optional): Type of the component. Defaults to 'card'.
            parameters (dict, optional): Component-specific parameters.
            transform (dict, optional): Transform data for the component.
            metadata (dict, optional): Additional metadata.
            node_names (dict, optional): Names of the Maya node associated with this component.
            is_viewed (bool, optional): Visibility state of the component. Defaults to True.
        """
        self.id = id if id is not None else str(uuid.uuid4())
        self.asset_id = asset_id
        self.name = name
        self.type = component_type
        self.parameters = parameters or {}
        self.transform = transform or {}
        self.metadata = metadata or {}
        self.node_names = node_names or {}
        self.is_viewed = is_viewed

    def to_dict(self):
        """Convert the component to a dictionary.

        Returns:
            dict: Dictionary representation of the component
        """
        result = {
            "id": self.id,
            "asset_id": self.asset_id,
            "name": self.name,
            "type": self.type,
            "parameters": self.parameters,
            "transform": self.transform,
            "metadata": self.metadata,
            "is_viewed": self.is_viewed,
            "visible": self.is_viewed,  # Alias for compatibility
        }

        # node_names must be a dict
        if not isinstance(self.node_names, dict):
            raise ValueError(f"node_names must be a dict, got {type(self.node_names)}")

        result["node_names"] = self.node_names

        return result

    @classmethod
    def from_dict(cls, data):
        """Create a HairComponent from a dictionary.

        Args:
            data (dict): Dictionary containing component data

        Returns:
            HairComponent: A new HairComponent instance
        """
        # node_names must be provided as dict
        node_names = data.get("node_names")
        if not isinstance(node_names, dict):
            raise ValueError(f"node_names must be a dict, got {type(node_names)}")

        return cls(
            id=data.get("id"),
            asset_id=data.get("asset_id"),
            name=data.get("name", "Unnamed Component"),
            component_type=data.get("type", "card"),
            parameters=data.get("parameters", {}),
            transform=data.get("transform", {}),
            metadata=data.get("metadata", {}),
            node_names=node_names,
            is_viewed=data.get("is_viewed", data.get("visible", True)),
        )


class HairProject(object):
    """Represents a hair project containing multiple components.

    Attributes:
        components (list): List of HairComponent instances in the project
        metadata (dict): Project metadata
        version (str): Project version string
    """

    def __init__(self):
        """Initialize a new HairProject."""
        self.components = []
        self.metadata = {}
        self.version = "1.0"

    def to_dict(self):
        """Convert the project to a dictionary.

        Returns:
            dict: Dictionary representation of the project
        """
        return {
            "version": self.version,
            "metadata": self.metadata,
            "components": [comp.to_dict() for comp in self.components],
        }

    @classmethod
    def from_dict(cls, data):
        """Create a HairProject from a dictionary.

        Args:
            data (dict): Dictionary containing project data

        Returns:
            HairProject: A new HairProject instance
        """
        project = cls()
        project.version = data.get("version", "1.0")
        project.metadata = data.get("metadata", {})
        project.components = [HairComponent.from_dict(comp_data) for comp_data in data.get("components", [])]
        return project
