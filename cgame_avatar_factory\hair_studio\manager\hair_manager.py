"""Hair Manager Module.

This module provides the HairManager class which serves as the central point for
managing hair assets, components, and their interactions in the Hair Studio tool.
"""

# Import built-in modules
from collections import OrderedDict

# Import standard library
import json
import logging
import uuid

# Import third-party modules
from qtpy import QtCore

# Import local modules
from cgame_avatar_factory.hair_studio.data.data_manager import DataManager
from cgame_avatar_factory.hair_studio.data.models import HairAsset
from cgame_avatar_factory.hair_studio.data.models import HairComponent
from cgame_avatar_factory.hair_studio.data.models import HairProject
from cgame_avatar_factory.hair_studio.maya_api import maya_api_instance
from cgame_avatar_factory.hair_studio.utils.data_util import async_preload, AsyncLoadManager


class HairManager(QtCore.QObject):
    """Manager class for hair studio operations.

    This class serves as the central point for managing hair assets, components,
    and their interactions. It handles data loading, saving, and communication
    between different parts of the application.
    """

    # Signal emitted when assets are loaded
    assets_loaded = QtCore.Signal(list)

    # Signal emitted when components are updated (full refresh needed)
    components_updated = QtCore.Signal(list)

    # Signal emitted when a single component's visibility changes (incremental update)
    component_visibility_changed = QtCore.Signal(str, bool)  # component_id, is_visible

    # Signal emitted when the selected component changes
    component_selected = QtCore.Signal(object)  # HairComponent or None

    # Add a signal for reporting errors to the UI
    operation_failed = QtCore.Signal(str)
    
    # 异步加载信号 - 必须在类定义时声明
    assets_loading_started = QtCore.Signal()
    assets_loading_finished = QtCore.Signal(object)  # result
    assets_loading_failed = QtCore.Signal(str)       # error_message
    assets_loading_progress = QtCore.Signal(int, str)  # progress, message

    def __init__(self, parent=None):
        """Initialize the HairManager.

        Args:
            parent: Parent QObject
        """
        super(HairManager, self).__init__(parent)

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Initialize data structures
        self._assets = OrderedDict()  # asset_id -> HairAsset
        self._components = OrderedDict()  # component_id -> HairComponent
        self._current_project = HairProject()
        self._selected_component_id = None

        # Initialize data manager for development and testing
        self._data_manager = DataManager()

        self._maya_api = maya_api_instance
        
        # 初始化异步加载管理器
        self._async_loader_manager = AsyncLoadManager(self)
        self._logger.info("HairManager initialized with async loading support")

    @async_preload(signal_prefix="assets_loading")
    def load_assets_from_data_manager(self):
        """Load assets from data manager into internal structure.
        
        This method is decorated with @async_preload, which automatically provides:
        - assets_loading_started signal
        - assets_loading_finished signal  
        - assets_loading_failed signal
        - assets_loading_progress signal
        """
        try:
            self._logger.info("Starting to load assets from data manager")
            
            # Get all assets from data manager
            asset_dicts = self._data_manager.get_assets()

            # Convert to HairAsset objects and store
            self._assets.clear()
            for asset_dict in asset_dicts:
                # Convert data manager format to HairAsset format
                # Use existing metadata if available, otherwise create new one
                metadata = asset_dict.get("metadata", {})
                if "file_path" not in metadata:
                    metadata["file_path"] = asset_dict.get("file_path")

                asset_data = {
                    "id": asset_dict.get("id"),
                    "name": asset_dict.get("name"),
                    "asset_type": asset_dict.get("asset_type"),
                    "sub_asset_type": asset_dict.get("sub_asset_type"),
                    "thumbnail": asset_dict.get("thumbnail"),
                    "metadata": metadata,
                }

                asset = HairAsset(**asset_data)
                self._assets[asset.id] = asset

            self._logger.info(f"Loaded {len(self._assets)} assets from data manager")
            
            # 发送资产加载完成信号
            self.assets_loaded.emit(self.get_assets())
            
            return self.get_assets()  # 返回结果供异步加载使用

        except Exception as e:
            self._logger.error(f"Failed to load assets from data manager: {e}")
            raise  # 重新抛出异常，让异步加载装饰器处理

    def start_async_asset_loading(self):
        """启动异步资产加载."""
        self._logger.info("Attempting to start async asset loading")
        if hasattr(self, '_async_loader_manager'):
            success = self._async_loader_manager.start_async_loading('load_assets_from_data_manager')
            if success:
                self._logger.info("Async asset loading started successfully")
            else:
                self._logger.warning("Failed to start async asset loading")
            return success
        else:
            self._logger.error("Async loader manager not initialized")
            return False

    def is_assets_loaded(self):
        """检查资产是否已加载."""
        if hasattr(self, '_async_loader_manager'):
            return self._async_loader_manager.is_loaded('load_assets_from_data_manager')
        return bool(self._assets)

    def is_assets_loading(self):
        """检查资产是否正在加载."""
        if hasattr(self, '_async_loader_manager'):
            return self._async_loader_manager.is_loading('load_assets_from_data_manager')
        return False

    def setup_preloading_with_callbacks(self, on_started=None, on_finished=None, on_failed=None):
        """设置预加载并连接回调函数.

        Args:
            on_started: 开始加载时的回调函数
            on_finished: 加载完成时的回调函数 (result)
            on_failed: 加载失败时的回调函数 (error_message)

        Returns:
            bool: 是否成功启动预加载
        """
        try:
            # 连接回调函数
            if on_started and hasattr(self, 'assets_loading_started'):
                self.assets_loading_started.connect(on_started)

            if on_finished and hasattr(self, 'assets_loading_finished'):
                self.assets_loading_finished.connect(on_finished)

            if on_failed and hasattr(self, 'assets_loading_failed'):
                self.assets_loading_failed.connect(on_failed)

            # 启动异步加载
            success = self.start_async_asset_loading()
            if success:
                self._logger.info("Hair assets preloading setup completed successfully")
            else:
                self._logger.warning("Failed to setup hair assets preloading")
            return success

        except Exception as e:
            self._logger.error(f"Failed to setup hair assets preloading: {e}", exc_info=True)
            return False

    # --- Public Slots for UI Interaction ---

    def add_component_from_asset(self, asset_id):
        """
        Slot to handle adding a new component from an asset library drop.
        This is the primary entry point for creating new hair components.
        """
        self._logger.debug("Adding component from asset: %s", asset_id)
        
        # 确保assets已加载 - 支持异步和同步加载
        if not self._assets:
            if self.is_assets_loading():
                # 如果正在异步加载，等待完成或直接同步加载
                self._logger.info("Assets are loading asynchronously, falling back to sync load")
                self.load_assets_from_data_manager()
            elif not self.is_assets_loaded():
                # 同步加载作为fallback
                self.load_assets_from_data_manager()

        asset = self._assets.get(asset_id)
        if not asset:
            error_msg = "Asset not found with ID: {}".format(asset_id)
            self._logger.error(error_msg)
            self.operation_failed.emit(error_msg)
            return

        # Create new component from asset
        component = self.create_component(asset_id)
        if component:
            self._logger.info("Successfully created component from asset: %s", asset_id)
            # Emit signal to update UI
            self.components_updated.emit(self.get_components())
        else:
            error_msg = "Failed to create component from asset: {}".format(asset_id)
            self._logger.error(error_msg)
            self.operation_failed.emit(error_msg)

    def delete_component(self, component_id):
        """Slot to handle deleting a component."""
        component = self._components.get(component_id)
        if not component:
            # ... (error handling)
            return

        # Call Maya API to delete the node(s)
        if not isinstance(component.node_names, dict):
            error_msg = f"Invalid node_names format, expected dict, got {type(component.node_names)}"
            self._logger.error(error_msg)
            self.operation_failed.emit(error_msg)
            return

        result = self._maya_api.delete_hair_component(
            {"node_names": component.node_names},
        )

        if result.get("success"):
            self._safe_remove_component(component_id, "Deleted component")
        else:
            error_msg = "Failed to delete Maya node: {}".format(
                result.get("error", "Unknown error"),
            )
            self._logger.error(error_msg)
            self.operation_failed.emit(error_msg)

    def set_component_visibility(self, component_id, is_visible):
        """Slot to handle component visibility changes.

        Args:
            component_id (str): ID of the component to modify
            is_visible (bool): New visibility state
        """
        component = self._components.get(component_id)
        if not component:
            error_msg = "Component not found with ID: {}".format(component_id)
            self._logger.error(error_msg)
            self.operation_failed.emit(error_msg)
            return

        # Call Maya API to set visibility
        if not isinstance(component.node_names, dict):
            error_msg = f"Invalid node_names format, expected dict, got {type(component.node_names)}"
            self._logger.error(error_msg)
            self.operation_failed.emit(error_msg)
            return

        result = self._maya_api.set_component_visibility(
            {"node_names": component.node_names},
            is_visible,
        )

        if result.get("success"):
            # Update component visibility state
            component.is_viewed = is_visible
            self._logger.info(
                "Set component visibility: %s -> %s",
                component.name,
                "visible" if is_visible else "hidden",
            )

            # Emit incremental update signal instead of full refresh
            # This allows UI to update only the affected component without rebuilding the entire list
            self.component_visibility_changed.emit(component_id, is_visible)

            # Note: We no longer emit components_updated for visibility changes
            # This prevents unnecessary full UI rebuilds and preserves selection state
        else:
            error_msg = "Failed to set component visibility: {}".format(
                result.get("error", "Unknown error"),
            )
            self._logger.error(error_msg)
            self.operation_failed.emit(error_msg)

    # --- Data Access Methods ---

    def reload_assets(self):
        """Reload assets from data manager."""
        self._data_manager.reload_assets()
        self.load_assets_from_data_manager()
        self.assets_loaded.emit(self.get_assets())

    def get_assets(self, asset_type=None, sub_asset_type=None):
        """Get assets, optionally filtered by type and sub-type.

        Args:
            asset_type (str, optional): Filter by asset type (card, xgen, curve)
            sub_asset_type (str, optional): Filter by sub-asset type (hair, eyebrow, beard)

        Returns:
            List[Dict]: List of asset dictionaries
        """
        # If no assets loaded, try to get from data manager directly
        if not self._assets:
            try:
                asset_dicts = self._data_manager.get_assets()
                return asset_dicts
            except Exception as e:
                self._logger.error(f"Failed to get assets from data manager: {e}")
                return []

        # Filter assets based on criteria
        filtered_assets = []
        for asset in self._assets.values():
            # Apply asset_type filter - 修复：使用asset.type而不是asset.asset_type
            if asset_type and asset.type != asset_type:
                continue

            # Apply sub_asset_type filter
            if sub_asset_type and asset.sub_asset_type != sub_asset_type:
                continue

            # Convert to dictionary format - 修复：使用asset.type而不是asset.asset_type
            asset_dict = {
                "id": asset.id,
                "name": asset.name,
                "asset_type": asset.type,  # 注意：返回格式仍使用asset_type键名，但从asset.type获取值
                "sub_asset_type": asset.sub_asset_type,
                "thumbnail": asset.thumbnail,
                "file_path": asset.metadata.get("file_path"),
                "metadata": asset.metadata,
            }
            filtered_assets.append(asset_dict)

        return filtered_assets

    def get_components(self):
        """Get a list of all components.

        Returns:
            list: List of component dictionaries (for compatibility with mock data)
        """
        # Return real components from internal storage
        return self.get_all_components()

    def get_all_components(self):
        """Get a list of all real HairComponent objects.

        Returns:
            list: List of HairComponent objects converted to dictionaries
        """
        components = []
        for component in self._components.values():
            components.append(component.to_dict())
        return components

    def get_component(self, component_id):
        """Get a component by its ID.

        Args:
            component_id (str): ID of the component to retrieve

        Returns:
            dict or None: The component data with the given ID, or None if not found
        """
        # Get component from internal storage
        component = self._components.get(component_id)
        if component:
            return component.to_dict()
        return None

    def get_selected_component(self):
        """Get the currently selected component.

        Returns:
            HairComponent or None: The selected component, or None if none is selected
        """
        if self._selected_component_id is None:
            return None
        return self.get_component(self._selected_component_id)

    def select_component(self, component_id):
        """Select a component by its ID.

        Args:
            component_id (str or None): ID of the component to select, or None to clear selection
        """
        if component_id is not None:
            # Check if component exists in internal storage
            component = self._components.get(component_id)
            if component is None:
                self._logger.warning("Component %s not found", component_id)
                return

            # Check if Maya nodes exist for this component
            if component.node_names and any(component.node_names.values()):
                check_result = self._maya_api.check_component_exists({"node_names": component.node_names})

                if check_result.get("success"):
                    # Nodes exist, select them in Maya
                    select_result = self._maya_api.select_component({"node_names": component.node_names})
                    if select_result.get("success"):
                        self._logger.info(
                            "Selected component '%s' in Maya: %s",
                            component.name,
                            select_result.get("selected_node"),
                        )
                    else:
                        self._logger.warning(
                            "Failed to select component '%s' in Maya: %s",
                            component.name,
                            select_result.get("error"),
                        )
                else:
                    # Nodes don't exist, show dialog and delete component
                    self._logger.warning(
                        "Component '%s' nodes missing in Maya: %s",
                        component.name,
                        check_result.get("missing_nodes"),
                    )

                    # Import QtWidgets for dialog
                    # Import third-party modules
                    from qtpy import QtWidgets

                    # Show dialog to user
                    QtWidgets.QMessageBox.information(
                        None,
                        "组件数据已失效",
                        f"组件 '{component.name}' 的Maya节点已被删除，将从列表中移除。",
                        QtWidgets.QMessageBox.Ok,
                    )

                    # Safely remove the invalid component
                    if self._safe_remove_component(component_id, "Removed invalid component"):
                        return
                    else:
                        # If removal failed, still clear selection to prevent issues
                        self._selected_component_id = None
                        self.component_selected.emit(None)
                        return

        self._selected_component_id = component_id

        # Emit the selected component data
        selected_component = self.get_component(component_id) if component_id else None
        self.component_selected.emit(selected_component)

    def _safe_remove_component(self, component_id, reason="Removed component"):
        """Safely remove a component from internal storage with proper cleanup.

        Args:
            component_id (str): ID of the component to remove
            reason (str): Reason for removal (for logging)
        """
        try:
            # Get component before removal for logging
            component = self._components.get(component_id)
            if not component:
                self._logger.warning("Cannot remove component %s: not found", component_id)
                return False

            component_name = component.name

            # Remove from internal storage using pop() for safety
            removed_component = self._components.pop(component_id, None)
            if removed_component:
                self._logger.info("%s: %s (ID: %s)", reason, component_name, component_id)

                # Check if this component was selected before clearing
                was_selected = self._selected_component_id == component_id

                # Clear selection if this component was selected
                if was_selected:
                    self._selected_component_id = None

                # Emit signal to update UI
                self.components_updated.emit(self.get_all_components())

                # If selection was cleared, emit selection change
                if was_selected:
                    self.component_selected.emit(None)

                return True
            else:
                self._logger.warning("Failed to remove component %s: pop returned None", component_id)
                return False

        except Exception as e:
            self._logger.error("Error removing component %s: %s", component_id, str(e))
            return False

    def create_component(self, asset_id, **kwargs):
        """Create a new component from an asset with enhanced error handling.

        Args:
            asset_id (str): ID of the asset to create a component from
            **kwargs: Additional attributes to set on the component

        Returns:
            dict: The created component data, or None if creation failed
        """
        try:
            # Validate asset_id
            if not asset_id:
                self._logger.error("create_component: asset_id is None or empty")
                return None

            if not isinstance(asset_id, str):
                self._logger.error(
                    "create_component: asset_id must be string, got %s",
                    type(asset_id),
                )
                return None

            # Check if asset exists
            asset = None
            all_assets = self._data_manager.get_assets()
            for a in all_assets:
                if a.get("id") == asset_id:
                    asset = a
                    break

            if not asset:
                self._logger.error(
                    "create_component: Asset not found for ID: %s",
                    asset_id,
                )
                available_ids = [a.get("id") for a in all_assets]
                self._logger.error("Available asset IDs: %s", available_ids)
                return None

            self._logger.info(
                "create_component: Creating component from asset '%s' (ID: %s)",
                asset.get("name", "Unknown"),
                asset_id,
            )

            # Create component directly in internal storage
            new_component = HairComponent(
                id=str(uuid.uuid4()),
                name=f"New {asset.get('name', 'Component')}",
                asset_id=asset_id,
                component_type=asset.get("asset_type", "card"),
            )

            # Store in internal components
            self._components[new_component.id] = new_component

            self._logger.info(
                "create_component: Component created successfully: %s",
                new_component.name,
            )

            # Convert to dict for return
            component_data = new_component.to_dict()

            # Select the new component
            self.select_component(component_data["id"])

            # Notify listeners
            self.components_updated.emit(self.get_components())

            return component_data

        except Exception as e:
            self._logger.error(
                "create_component: Unexpected error: %s",
                str(e),
                exc_info=True,
            )
            return None

    def update_component(self, component_id, **kwargs):
        """Update a component's properties.

        Args:
            component_id (str): ID of the component to update
            **kwargs: Attributes to update on the component

        Returns:
            bool: True if the component was updated, False otherwise
        """
        # Update component in internal storage
        component = self._components.get(component_id)
        if not component:
            self._logger.warning("Component %s not found for update", component_id)
            return False

        # Update component properties
        for key, value in kwargs.items():
            if hasattr(component, key):
                setattr(component, key, value)
            else:
                self._logger.warning("Component property %s not found", key)

        # Notify listeners
        self.components_updated.emit(self.get_components())

        # If this is the selected component, emit selection changed
        if component_id == self._selected_component_id:
            component_data = self.get_component(component_id)
            self.component_selected.emit(component_data)

        self._logger.info("Updated component: %s", component_id)
        return True

    def save_project(self, filepath):
        """Save the current project to a file.

        Args:
            filepath (str): Path to save the project to

        Returns:
            bool: True if the project was saved successfully, False otherwise
        """
        try:
            # Prepare data for serialization
            project_data = {
                "version": self._current_project.version,
                "metadata": self._current_project.metadata,
                "components": [
                    {
                        "id": comp.id,
                        "asset_id": comp.asset_id,
                        "name": comp.name,
                        "type": comp.type,
                        "parameters": comp.parameters,
                        "transform": comp.transform,
                        "metadata": comp.metadata,
                    }
                    for comp in self._current_project.components
                ],
            }

            # Write to file
            with open(filepath, "w") as f:
                json.dump(project_data, f, indent=4)

            self._logger.info("Project saved to %s", filepath)
            return True

        except Exception as e:
            self._logger.error("Failed to save project: %s", str(e), exc_info=True)
            return False

    def load_project(self, filepath):
        """Load a project from a file.

        Args:
            filepath (str): Path to the project file to load

        Returns:
            bool: True if the project was loaded successfully, False otherwise
        """
        try:
            # Read from file
            with open(filepath, "r") as f:
                project_data = json.load(f)

            # Clear current state
            self._components.clear()
            self._current_project = HairProject()
            self._selected_component_id = None

            # Update project metadata
            self._current_project.version = project_data.get("version", "1.0")
            self._current_project.metadata = project_data.get("metadata", {})

            # Create components
            for comp_data in project_data.get("components", []):
                component = HairComponent(
                    id=comp_data.get("id", str(uuid.uuid4())),
                    asset_id=comp_data["asset_id"],
                    name=comp_data.get("name", "Unnamed Component"),
                    type=comp_data.get("type", "card"),
                    parameters=comp_data.get("parameters", {}),
                    transform=comp_data.get("transform", {}),
                    metadata=comp_data.get("metadata", {}),
                )
                self._components[component.id] = component
                self._current_project.components.append(component)

            # Notify listeners
            self.components_updated.emit(self.get_components())

            self._logger.info("Project loaded from %s", filepath)
            return True

        except Exception as e:
            self._logger.error("Failed to load project: %s", str(e), exc_info=True)
            return False

    def __del__(self):
        """析构函数，确保资源清理."""
        if hasattr(self, '_async_loader_manager'):
            self._async_loader_manager.cleanup_all_workers()

    