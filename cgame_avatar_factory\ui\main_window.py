# Import built-in modules
import logging
import os

# Import third-party modules
import dayu_widgets
from dayu_widgets import dayu_theme
import lightbox_ui.log
from maya.app.general.mayaMixin import MayaQWidgetDockableMixin
from pymel import core as pm
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.common import constants as const
from cgame_avatar_factory.common.ui.layout import FramelessHLayout
from cgame_avatar_factory.common.ui.layout import FramelessVLayout

from cgame_avatar_factory.hair_studio.utils.data_util import analyze_time

@dayu_widgets.utils.add_settings(const.ORGANIZATION_NAME, const.PACKAGE_NAME, event_name="closeEvent")
class MainWidget(QtWidgets.QWidget, MayaQWidgetDockableMixin):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(
            const.APP_TITLE_TEMPLATE.format(
                app_name=const.APP_NAME,
                app_version=const.APP_VERSION,
            ),
        )
        self.setObjectName(self.__class__.__name__)
        cid = "%sWorkspaceControl" % self.objectName()
        if pm.workspaceControl(cid, q=1, ex=1):
            pm.workspaceControl(cid, e=1, close=1)
            pm.deleteUI(cid)
        self.setDockableParameters(True)
        self.logger = logging.getLogger(__name__)

        self.setup_ui()
        self.setup_logger()

        self.setWindowTitle(
            "Adam DNA Creator {}".format(
                "for {}".format(const.PROJECT_ABBR) if const.PROJECT_ABBR else "",
            ),
        )

        QtCore.QTimer.singleShot(100, self.check_lib_path)

        # 启动异步资产预加载
        self._start_hair_assets_preloading()

    def check_lib_path(self):
        """Check if the project resource path exists.

        Displays a warning message box if the resource path does not exist,
        prompting the user to check the path configuration.
        """
        path = const.PROJECT_RESOURCES_PATH
        if not os.path.exists(path):
            msg_box = QtWidgets.QMessageBox(
                QtWidgets.QMessageBox.Warning,
                "警告",
                f"资源路径不存在：\n{path}\n请检查路径是否正确。",
                QtWidgets.QMessageBox.Ok,
                self,
            )
            msg_box.setWindowFlags(QtCore.Qt.Tool | QtCore.Qt.WindowStaysOnTopHint)
            maya_window = self.window()
            maya_geometry = maya_window.geometry()

            msg_box.setFixedSize(msg_box.sizeHint())
            x = maya_geometry.x() + (maya_geometry.width() - msg_box.width()) // 2
            y = maya_geometry.y() + (maya_geometry.height() - msg_box.height()) // 2
            msg_box.move(x, y)
            msg_box.exec_()
            return

    def setup_logger(self):
        """Set up the logging system for the application.

        Configures log handlers, sets the appropriate log level based on configuration,
        and initializes the log controller for displaying logs in the UI.
        """
        log_level = const.LOG_LEVEL
        is_debug_mode = log_level == "DEBUG"
        while self.logger.handlers:
            self.logger.removeHandler(self.logger.handlers[0])

        self.log_handler = logging.StreamHandler()
        self.logger.addHandler(self.log_handler)

        self.log_handler.setLevel(logging._nameToLevel[log_level])

        if is_debug_mode:
            logging.getLogger().setLevel(logging.DEBUG)
            self.log_controller = lightbox_ui.log.setup_log(parent=self)
        else:
            self.log_controller = lightbox_ui.log.setup_log(parent=self.log_widget)

    def setup_ui(self):
        """Set up the main user interface components.

        Creates the main layout and initializes all UI components including
        the menu bar, central area, and log widget.
        """
        self.main_layout = FramelessVLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        self.setLayout(self.main_layout)

        self.setup_menu_bar(self.main_layout)
        self.setup_central_area(self.main_layout)

        self.setup_log_widget()

    def setup_menu_bar(self, parent_layout):
        """Set up the application menu bar.

        Creates the menu bar with file, edit, and settings menus and adds it to the parent layout.

        Args:
            parent_layout: The parent layout to add the menu bar to
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.components.menu_tab import DNAMenuTabWidget

        self.menu_bar = DNAMenuTabWidget()
        self.menu_bar.frameless().background_color(const.DAYU_BG_IN_COLOR)

        self.file_menu = dayu_widgets.MMenu(parent=self.menu_bar)
        self.edit_menu = dayu_widgets.MMenu(parent=self.menu_bar)
        self.settings_menu = dayu_widgets.MMenu(parent=self.menu_bar)

        self.menu_bar.set_dayu_size(dayu_theme.small)

        item_list = [
            {"text": "文件", "menu": self.file_menu},
            {"text": "编辑", "menu": self.edit_menu},
            {"text": "设置", "menu": self.settings_menu},
        ]
        for index, data_dict in enumerate(item_list):
            self.menu_bar.add_menu(data_dict, index)

        parent_layout.addWidget(self.menu_bar)

    def setup_central_area(self, parent_layout):
        """Set up the central area of the application.

        Creates the central container and layout, initializes the base merge page,
        materials lab page, and left area components.

        Args:
            parent_layout: The parent layout to add the central area to
        """
        self.central_area_container = QtWidgets.QFrame()
        self.central_area_layout = FramelessHLayout()
        self.central_area_container.setLayout(self.central_area_layout)
        self.setup_base_merge_page()
        self.setup_body_base_merge_page()
        self.setup_materials_lab_page()
        self.setup_animation_theater_page()
        self.setup_left_area(self.central_area_layout)

        parent_layout.addWidget(self.central_area_container)

    def setup_left_area(self, parent_layout):
        """Set up the left area of the application.

        Creates the left area container and layout, and initializes the side tab bar.

        Args:
            parent_layout: The parent layout to add the left area to
        """
        self.left_area_container = QtWidgets.QFrame()
        self.left_area_container.setFrameShape(QtWidgets.QFrame.NoFrame)

        self.left_area_layout = FramelessHLayout()
        self.left_area_container.setLayout(self.left_area_layout)

        self.setup_side_tab_bar(self.left_area_layout)

        parent_layout.addWidget(self.left_area_container)

    def setup_side_tab_bar(self, parent_layout):
        """Set up the side tab bar for navigation.

        Creates a vertical tab bar with various functional pages including face morphing center,
        body workshop, materials lab, hair studio, animation theater, costume workshop, and export center.

        Args:
            parent_layout: The parent layout to add the side tab bar to
        """
        # Import third-party modules
        from dayu_widgets.loading import MLoadingWrapper

        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.components.menu_tab import MMenuTabWidget

        self.side_tab_bar = MMenuTabWidget(orientation=QtCore.Qt.Vertical, parent=self)
        self.side_tab_bar.set_dayu_size(dayu_theme.large)
        self.stack_widget = QtWidgets.QStackedWidget()

        self.init_hair_studio_page()
        
        tab_menu_list = [
            {
                "text": "面部整形中心",
                "widget": self.face_base_merge_page,
                "clicked": self.show_base_merge_page,
                "checked": True,
            },
            {
                "text": "身体工坊",
                "widget": self.body_base_merge_page,
                "clicked": self.show_body_workshop_page,
            },
            {
                "text": "材质实验室",
                "widget": self.materials_lab_page,
                "clicked": self.show_materials_lab_page,
            },
            {
                "text": "毛发工作室",
                "widget": self.hair_studio_tab,
                "clicked": self.show_hair_studio_page,
            },
            {
                "text": "动画剧场",
                "widget": self.animation_theater_page,
                "clicked": self.show_animation_theater_page,
            },
            {
                "text": "服装工房",
                "widget": dayu_widgets.MLabel("服装工房"),
                "clicked": self.show_costume_workshop_page,
            },
            {
                "text": "导出中心",
                "widget": dayu_widgets.MLabel("导出中心"),
                "clicked": self.show_export_center_page,
            },
        ]

        for id, menu in enumerate(tab_menu_list):
            self.side_tab_bar.add_menu(menu, id)
            self.stack_widget.addWidget(menu.get("widget"))

        self.loading_wrapper = MLoadingWrapper(widget=self.stack_widget, loading=False)
        self.show_base_merge_page()

        parent_layout.setSpacing(0)
        parent_layout.setContentsMargins(0, 0, 0, 0)
        parent_layout.addWidget(self.side_tab_bar)
        parent_layout.addWidget(self.loading_wrapper)

        

    def _start_hair_assets_preloading(self):
        """启动毛发资产的异步预加载."""
        try:
            if hasattr(self.hair_studio_tab, '_hair_manager'):

                hair_manager = self.hair_studio_tab._hair_manager
                if not hair_manager:
                    self.logger.warning("Hair manager not found, skipping asset preloading")
                    return
                
                # 连接异步加载信号（装饰器自动创建的信号）
                if hasattr(hair_manager, 'assets_loading_started'):
                    hair_manager.assets_loading_started.connect(self._on_hair_assets_loading_started)
                if hasattr(hair_manager, 'assets_loading_finished'):
                    hair_manager.assets_loading_finished.connect(self._on_hair_assets_loading_finished)
                if hasattr(hair_manager, 'assets_loading_failed'):
                    hair_manager.assets_loading_failed.connect(self._on_hair_assets_loading_failed)
                
                # 启动异步加载
                success = hair_manager.start_async_asset_loading()
                if success:
                    self.logger.info("Hair assets async loading started successfully")
                else:
                    self.logger.warning("Failed to start hair assets async loading")
            else:
                self.logger.warning("Hair manager not found, skipping asset preloading")
                
        except Exception as e:
            self.logger.error(f"Failed to start hair assets preloading: {e}", exc_info=True)
    
    def _on_hair_assets_loading_started(self):
        """毛发资产开始加载的回调."""
        self.logger.debug("Hair assets loading started")
    
    def _on_hair_assets_loading_finished(self, result):
        """毛发资产加载完成的回调."""
        self.logger.info(f"Hair assets loading completed successfully, loaded {len(result) if result else 0} assets")
    
    def _on_hair_assets_loading_failed(self, error_msg):
        """毛发资产加载失败的回调."""
        self.logger.error(f"Hair assets loading failed: {error_msg}")

    def show_body_workshop_page(self):
        """Display the body workshop page."""
        self.stack_widget.setCurrentIndex(1)
        # TODO: Add body workshop specific logic here

    def show_materials_lab_page(self):
        """Display the materials lab page."""
        self.stack_widget.setCurrentIndex(2)
        # TODO: Add materials lab specific logic here

    def show_hair_studio_page(self):
        """Display the hair studio page."""
        self.stack_widget.setCurrentIndex(3)
        self.hair_studio_tab.set_default_tab()
        # TODO: Add hair studio specific logic here

    def show_animation_theater_page(self):
        """Display the animation theater page."""
        self.stack_widget.setCurrentIndex(4)
        # TODO: Add animation theater specific logic here

    def show_costume_workshop_page(self):
        """Display the costume workshop page."""
        self.stack_widget.setCurrentIndex(5)
        # TODO: Add costume workshop specific logic here

    def show_export_center_page(self):
        """Display the export center page."""
        self.stack_widget.setCurrentIndex(6)
        # TODO: Add export center specific logic here

    def show_base_merge_page(self):
        """Display the base merge page.

        Sets up the DNA library and shows the base merge page in the stack widget.
        """
        self.stack_widget.setCurrentIndex(0)

    def setup_log_widget(self):
        """Set up the log widget dialog.

        Creates a dialog for displaying application logs with appropriate layout and title.
        """
        self.log_widget = QtWidgets.QDialog(parent=self)
        self.log_widget_layout = FramelessVLayout()
        self.log_widget.setLayout(self.log_widget_layout)
        self.log_widget.setWindowTitle("日志")

    @analyze_time
    def init_hair_studio_page(self):
        """Set up the hair studio page."""
        # Import local modules
        from cgame_avatar_factory.hair_studio.ui.hair_studio_tab import HairStudioTab

        self.hair_studio_tab = HairStudioTab(parent=self.stack_widget)

    @analyze_time
    def setup_base_merge_page(self):
        """Set up the base merge page.

        Initializes the base merge page and connects all necessary signals to their respective slots
        for handling DNA operations, area selection, mirror mode, and other UI interactions.
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.pages.face_base_merge import FaceBaseMergePage

        self.face_base_merge_page = FaceBaseMergePage(self)
    
    @analyze_time
    def setup_body_base_merge_page(self):
        """Set up the body base merge page.

        Initializes the base merge page and connects all necessary signals to their respective slots
        for handling DNA operations, area selection, mirror mode, and other UI interactions.
        """
        # Import local modules
        from cgame_avatar_factory.body_workshop.ui.pages.body_base_merge import BodyBaseMergePage

        self.body_base_merge_page = BodyBaseMergePage(self)

    @analyze_time
    def setup_materials_lab_page(self):
        """Set up the materials laboratory page.

        Initializes the materials lab widget using the singleton pattern.
        """
        # Import local modules
        from cgame_avatar_factory.materials_lab.ui.materials_lab_widget import MaterialsLabWidget

        self.materials_lab_page = MaterialsLabWidget.instance()

    @analyze_time
    def setup_animation_theater_page(self):
        """Set up the animation theater page.

        Initializes the animation theater page widget.
        """
        # Import local modules
        from cgame_avatar_factory.animation_theater.ui.animation_theater_page import AnimationTheaterPage

        # Create as a normal widget; parent it to the central container (it will be re-parented by QStackedWidget.addWidget)
        self.animation_theater_page = AnimationTheaterPage(parent=self.central_area_container)

    def closeEvent(self, event):
        """Handle window close event.

        Called when the application window is being closed.
        The actual cleanup is handled by _handle_close_event to avoid duplication.

        Args:
            event: The close event object
        """
        # Ensure cleanup is performed (handled by _handle_close_event via eventFilter)
        self._handle_close_event()

        # Call parent closeEvent
        super().closeEvent(event)

    def eventFilter(self, obj, event):
        """
        Event filter for handling close, hide, and window state change events.
        """
        if obj == self:
            if event.type() == QtCore.QEvent.Close:
                self._handle_close_event()
                return False
            elif event.type() == QtCore.QEvent.Hide:
                if not self.isVisible() and not self._is_closing:
                    self._handle_close_event()
                return False
            elif event.type() == QtCore.QEvent.WindowStateChange:
                return False

        return super(MainWidget, self).eventFilter(obj, event)

    def _handle_close_event(self):
        """
        Unified handler for close events.

        Ensures that the close event is only processed once by checking
        the _is_closing flag. Also stops export monitoring.
        """
        # Import local modules
        from cgame_avatar_factory.common.reporter.event_monitor import stop_event_monitoring

        if self._is_closing:
            return
        self._is_closing = True

        # Clean up export monitoring when tool is closed
        try:
            stop_event_monitoring()
        except Exception as e:
            self.logger.error(f"Failed to stop export monitoring: {e}")
