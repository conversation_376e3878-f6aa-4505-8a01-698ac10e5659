"""GS Converter Widget Module.

This module provides the GS converter widget for the Hair Studio tool.
It allows users to convert hair components using GS conversion.
"""

# Import built-in modules
# Import standard library
import logging

# Import third-party modules
# Import dayu widgets
from dayu_widgets import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dayu_widgets import <PERSON><PERSON>abe<PERSON>
from dayu_widgets import MPushButton
from dayu_widgets import MRadioButton

# Import Qt modules
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.hair_studio.constants import DEFAULT_MARGIN
from cgame_avatar_factory.hair_studio.constants import DEFAULT_SPACING
from cgame_avatar_factory.hair_studio.constants import GS_CONVERTER_STYLE
from cgame_avatar_factory.hair_studio.maya_api.gs_convert_api import GSConvertAPI


class GSConverterWidget(QtWidgets.QWidget):
    """GS Converter Widget.

    This widget provides GS conversion controls for hair components.
    It does not update based on component selection.
    """

    def __init__(self, parent=None):
        """Initialize the GSConverterWidget.

        Args:
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super(GSConverterWidget, self).__init__(parent)
        self.setObjectName("GSConverterWidget")

        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Initialize GS Convert API
        self._gs_api = GSConvertAPI()
        self._setup_progress_callback()

        # Initialize UI
        self.setup_ui()

        # Apply styles
        self.setStyleSheet(GS_CONVERTER_STYLE)

    def setup_ui(self):
        """Set up the user interface components."""

        # Main layout with consistent margins (上下布局)
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(
            DEFAULT_MARGIN,
            DEFAULT_MARGIN,
            DEFAULT_MARGIN,
            DEFAULT_MARGIN,
        )
        main_layout.setSpacing(DEFAULT_SPACING)

        # Create converter controls container (转换系列组件)
        converter_container_group = QtWidgets.QGroupBox("转换设置")
        converter_layout = QtWidgets.QHBoxLayout(converter_container_group)
        converter_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        converter_layout.setSpacing(DEFAULT_SPACING)

        # Left side: Controls (左边：单选按钮和复选框)
        controls_container = QtWidgets.QWidget()
        controls_layout = QtWidgets.QVBoxLayout(controls_container)
        controls_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        controls_layout.setSpacing(DEFAULT_SPACING)

        # Top section: Radio buttons (上面：单选按钮组)
        radio_container = QtWidgets.QWidget()
        radio_layout = QtWidgets.QHBoxLayout(radio_container)
        radio_layout.setContentsMargins(DEFAULT_MARGIN / 2, DEFAULT_MARGIN / 2, DEFAULT_MARGIN / 2, DEFAULT_MARGIN / 2)
        radio_layout.setSpacing(DEFAULT_SPACING // 2)

        # Create radio buttons using MRadioButton
        self.standard_mode_rb = MRadioButton("标准模式")
        self.compatible_mode_rb = MRadioButton("兼容模式")

        # Set default selection
        self.standard_mode_rb.setChecked(True)

        # Add radio buttons to radio layout
        radio_layout.addWidget(self.standard_mode_rb)
        radio_layout.addWidget(self.compatible_mode_rb)

        # Bottom section: Checkboxes (下面：复选框组)
        checkbox_container = QtWidgets.QWidget()
        checkbox_layout = QtWidgets.QHBoxLayout(checkbox_container)
        checkbox_layout.setContentsMargins(
            DEFAULT_MARGIN / 2,
            DEFAULT_MARGIN / 2,
            DEFAULT_MARGIN / 2,
            DEFAULT_MARGIN / 2,
        )
        checkbox_layout.setSpacing(DEFAULT_SPACING // 2)

        # Create checkboxes using MCheckBox
        self.extract_curves_cb = MCheckBox("仅提取曲线")
        self.vertex_match_cb = MCheckBox("逐顶点匹配")

        # Set default states
        self.extract_curves_cb.setChecked(False)
        self.vertex_match_cb.setChecked(False)

        # Add checkboxes to checkbox layout
        checkbox_layout.addWidget(self.extract_curves_cb)
        checkbox_layout.addWidget(self.vertex_match_cb)

        # Add radio and checkbox containers to controls layout
        controls_layout.addWidget(radio_container)
        controls_layout.addWidget(checkbox_container)

        # Right side: Convert button (右边：转换按钮)
        button_container = QtWidgets.QWidget()
        button_layout = QtWidgets.QVBoxLayout(button_container)
        button_layout.setContentsMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN)
        button_layout.setSpacing(DEFAULT_SPACING)

        # Add stretch to center the button vertically
        button_layout.addStretch()

        # Create convert button
        self.convert_btn = MPushButton("转换")
        self.convert_btn.clicked.connect(self._on_convert)
        button_layout.addWidget(self.convert_btn)

        # Add stretch to center the button vertically
        button_layout.addStretch()

        # Add left and right containers to converter layout with separator
        converter_layout.addWidget(controls_container, 2)  # Give more space to controls

        # Add vertical separator line (中间竖线分隔)
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.VLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        separator.setStyleSheet("color: #5E5E5E; background-color: #5E5E5E;")
        separator.setFixedWidth(2)
        converter_layout.addWidget(separator)

        converter_layout.addWidget(button_container, 1)  # Fixed space for button

        # Add converter container to main layout
        main_layout.addWidget(converter_container_group)

        # Create progress area (initially hidden, like original UI)
        self.progress_container = QtWidgets.QWidget()
        progress_layout = QtWidgets.QVBoxLayout(self.progress_container)
        progress_layout.setContentsMargins(0, 0, 0, 0)
        progress_layout.setSpacing(DEFAULT_SPACING // 2)

        self.progress_bar = QtWidgets.QProgressBar()
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)

        self.progress_label = MLabel("已处理 0 / 0")
        self.progress_label.setAlignment(QtCore.Qt.AlignCenter)

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.progress_label)

        # Initially hide progress container (like original UI)
        self.progress_container.setVisible(False)
        main_layout.addWidget(self.progress_container)

        # Add spacer to push panels to top
        spacer = QtWidgets.QSpacerItem(20, 800, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        main_layout.addItem(spacer)

        # Add stretch to push content to the top
        main_layout.addStretch()

    def _setup_progress_callback(self):
        """Setup progress callback for GS API."""

        def progress_callback(current, total, message):
            """Progress callback function for GS conversion operations."""
            self._logger.info(f"GS转换进度: {message}")
            # TODO: 如果需要UI进度条，可以在这里添加
            # 例如：self.progress_bar.setValue(current)

        self._gs_api.set_progress_callback(progress_callback)

    def _create_separator(self):
        """Create a styled separator widget."""
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        return separator

    def _on_convert(self):
        """Handle convert button click.

        根据UI状态执行相应的转换：
        - 检查"仅提取曲线"复选框状态
        - 检查"标准模式"/"兼容模式"单选按钮状态
        - 执行相应的转换逻辑
        完全复制原始UI的进度条显示逻辑。
        """
        # 获取UI状态
        only_extract_curve = self.extract_curves_cb.isChecked()
        do_vertex_match = self.vertex_match_cb.isChecked()
        is_standard_mode = self.standard_mode_rb.isChecked()

        mode_text = "标准模式" if is_standard_mode else "兼容模式"
        action_text = "仅提取曲线" if only_extract_curve else "完整转换"
        vertex_match_text = "逐顶点匹配" if do_vertex_match else "无逐顶点匹配"

        self._logger.info(f"开始执行转换: {mode_text} - {action_text} - {vertex_match_text}")

        try:
            # === 完全复制原始UI的进度条逻辑 ===
            # 显示进度区并重置
            self.progress_container.setVisible(True)
            self.progress_bar.setValue(0)
            self.progress_label.setText("已处理 0 / 0")

            # 根据模式选择执行相应的转换
            if is_standard_mode:
                # 标准模式转换
                success = self._gs_api.standard_convert(
                    do_vertex_match=do_vertex_match,  # 使用UI中的逐顶点匹配设置
                    only_create_curve=only_extract_curve,
                    progress_bar=self.progress_bar,
                    progress_text=self.progress_label,
                )
            else:
                # 兼容模式转换
                success = self._gs_api.compatibility_convert(
                    only_create_curve=only_extract_curve,
                    progress_bar=self.progress_bar,
                    progress_text=self.progress_label,
                )

            # 隐藏进度区（与原始UI一致）
            self.progress_container.setVisible(False)

            if success:
                self._logger.info(f"{mode_text} - {action_text} 完成")
                # TODO: 可以添加成功提示UI
            else:
                self._logger.error(f"{mode_text} - {action_text} 失败")
                # TODO: 可以添加失败提示UI

        except Exception as e:
            # 确保异常时也隐藏进度区
            self.progress_container.setVisible(False)
            self._logger.error(f"{mode_text} - {action_text} 异常: {e}")
            # TODO: 可以添加异常提示UI
