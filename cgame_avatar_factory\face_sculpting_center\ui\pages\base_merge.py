# Import built-in modules
import logging

# Import third-party modules
import dayu_widgets
from dayu_widgets import MTabWidget
from maya import cmds
import maya.utils as maya_utils
from qtpy import QtCore
from qtpy import QtWidgets
from qtpy.QtCore import QAbstractAnimation

# Import local modules
from cgame_avatar_factory.common import constants as const
import cgame_avatar_factory.common.state.scene_state_manager as ScenStateManager
from cgame_avatar_factory.common.ui.layout import FramelessHLayout
from cgame_avatar_factory.common.ui.layout import FramelessVLayout
from cgame_avatar_factory.common.ui.mixin.style_mixin import MStyleMixin
from cgame_avatar_factory.face_sculpting_center import constants as face_const
import cgame_avatar_factory.face_sculpting_center.build_face.face_controllers as customization


class BaseMergePage(QtWidgets.QFrame):
    # Add signals for state changes
    sig_weights_changed = QtCore.Signal(list)
    sig_components_changed = QtCore.Signal(list)
    sig_area_changed = QtCore.Signal(list)
    sig_pivot_move_ready = QtCore.Signal()

    def __init__(self, parent=None):
        """Initialize the BaseMergePage instance.

        Creates a new instance of the BaseMergePage class, setting up logging and scene call.

        Args:
            parent: The parent widget, defaults to None
        """
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        # Track reset buttons across face control panels
        self._reset_btns = []

    def setup_ui(self):
        """Set up the user interface for the BaseMergePage.

        Initializes the main layout, tab widget, and various UI components.
        Creates three tabs: work area, parametric parts, and AI assistance.
        Sets up the operation area, status bar, and floating panel.
        """
        self.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.main_layout = FramelessVLayout()
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        self.setLayout(self.main_layout)

        # Create main tab widget
        # Use dayu_widgets styled tab widget
        self.tab_widget = MStyleMixin.instance_wrapper(MTabWidget())
        self.tab_widget.setTabPosition(QtWidgets.QTabWidget.North)
        self.main_layout.addWidget(self.tab_widget)

        # --- Tab 1: original content ---
        self.work_area_tab = QtWidgets.QWidget()
        self.work_area_layout = FramelessVLayout()
        self.work_area_tab.setLayout(self.work_area_layout)

        # Insert all original content into work_area_layout
        self.ring = None
        self.pivot_move = None
        self.dna_merge_container = None
        self.dna_merge_layout = None
        self.setup_operation_area(self.work_area_layout)
        self.work_area_layout.addSpacing(5)

    def setup_operation_area(self, parent_layout):
        """Set up the operation area of the UI.

        Creates the main operation container with horizontal layout and adds
        the DNA merge area and DNA library components.

        Args:
            parent_layout: The parent layout to add the operation area to
        """
        self.splitter = QtWidgets.QSplitter(QtCore.Qt.Horizontal)
        self.splitter.setChildrenCollapsible(False)
        self.splitter.setHandleWidth(8)
        self._style_splitter_handles()

        self.operation_container = QtWidgets.QFrame()
        self.operation_layout = FramelessHLayout()
        self.operation_container.setLayout(self.operation_layout)
        self.setup_face_operation_area(self.splitter)
        self.setup_dna_merge_area(self.splitter)
        self.operation_layout.addWidget(self.splitter)
        self.setup_dna_lib()
        self.splitter.setSizes([900, 600, 600])
        parent_layout.addWidget(self.operation_container)

    def add_bottom_export_area(self, parent_layout):
        """Set up the bottom export area of the UI.

        Creates a container with buttons for LOD generation, model collapsing,
        and exporting functionality. Includes a dropdown for selecting export type
        and connects all buttons to their respective handlers.

        Args:
            parent_layout: The parent layout to add the export area to
        """
        self.bottom_export_area_container = MStyleMixin.cls_wrapper(QtWidgets.QFrame)()
        self.bottom_export_area_layout = FramelessHLayout()
        self.bottom_export_area_layout.setContentsMargins(5, 5, 5, 5)
        self.bottom_export_area_container.setLayout(self.bottom_export_area_layout)

        self.bottom_export_area_container.frameless().border_radius(5).background_color(
            const.DAYU_BG_IN_COLOR,
        )

        self.generate_lod_btn = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton())
        self.generate_lod_btn.text_beside_icon().svg("export.svg").small()
        self.generate_lod_btn.transparent_background(modifier=":disabled")
        self.generate_lod_btn.setText("LOD生成")

        self.collapse_model_btn = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton())
        self.collapse_model_btn.text_beside_icon().svg("collapse.svg").small()
        self.collapse_model_btn.setText("塌陷模型")
        self.collapse_model_btn.setCursor(QtCore.Qt.PointingHandCursor)
        self.collapse_model_btn.setStyleSheet(
            f"""
            QToolButton {{
                background-color: {const.APP_PRIMARY_COLOR};
            }}
            QToolButton:pressed {{
                background-color: {const.PRIMARY_COLOR};
            }}
        """
        )

        self.bottom_export_area_layout.addWidget(self.generate_lod_btn)
        self.bottom_export_area_layout.addWidget(self.collapse_model_btn)

        self.bottom_export_area_layout.addStretch(1)

        export_layout = FramelessHLayout()

        self.export_type_combobox = MStyleMixin.instance_wrapper(dayu_widgets.MComboBox())
        self.export_type_combobox.frameless().border_radius(5).background_color(const.DAYU_BG_COLOR)
        self.export_type_combobox.addItems(["导出融合结果", "GOZ", "导出至库中"])
        self.export_type_combobox.setCurrentText("导出融合结果")
        self.export_type_combobox.setFixedWidth(180)

        self.export_dna_btn = MStyleMixin.instance_wrapper(dayu_widgets.MToolButton())
        self.export_dna_btn.text_beside_icon().svg("export.svg").small()
        self.export_dna_btn.setText(" 导出")
        self.export_dna_btn.setCursor(QtCore.Qt.PointingHandCursor)
        self.export_dna_btn.setStyleSheet(
            f"""
            QToolButton {{
                background-color: {const.APP_PRIMARY_COLOR};
            }}
            QToolButton:pressed {{
                background-color: {const.DAYU_PRIMARY_7};
            }}
        """
        )

        export_layout.addWidget(self.export_type_combobox)
        export_layout.addWidget(self.export_dna_btn)
        self.bottom_export_area_layout.addLayout(export_layout)

        parent_layout.addWidget(self.bottom_export_area_container)

    def setup_dna_merge_area(self, parent_layout):
        """Set up the DNA merge area with ring widget and pivot move.

        Creates a container for the DNA merge functionality, including the functional area,
        DNA ring widget, and export controls. This is the main workspace for DNA merging operations.

        Args:
            parent_layout: The parent layout to add the DNA merge area to
        """
        self.dna_merge_container = QtWidgets.QFrame(parent=self)
        self.dna_merge_layout = FramelessVLayout()
        self.dna_merge_container.setLayout(self.dna_merge_layout)

        maya_utils.executeDeferred(self.setup_functional_area, self.dna_merge_layout)
        self.dna_merge_layout.addStretch(1)
        self.setup_dna_ring(self.dna_merge_layout)
        self.dna_merge_layout.addStretch(1)
        self.add_bottom_export_area(self.dna_merge_layout)
        parent_layout.addWidget(self.dna_merge_container)

    def setup_dna_ring(self, parent_layout):
        """Set up the DNA ring widget and initialize pivot move.

        Creates the drag point ring widget for DNA component manipulation and
        initializes the pivot move functionality. Sets up signal connections and
        emits the pivot move ready signal.

        Args:
            parent_layout: The parent layout to add the DNA ring to
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.dna_merge_ring.drag_point_ring import DragPointRingWidget
        from cgame_avatar_factory.face_sculpting_center.ui.pivot_move import UIPivotMove

        self.logger.info("Setting up DNA ring")

        if not self.ring:
            self.ring = DragPointRingWidget(parent=self)
            self.pivot_move = UIPivotMove(ring_widget=self.ring, parent=self)
            maya_utils.executeDeferred(self._setup_ring_connections)
            maya_utils.executeDeferred(self.sig_pivot_move_ready.emit)

        parent_layout.addWidget(self.ring)
        self.logger.info("DNA ring setup completed")

    def _style_splitter_handles(self):
        """Style the splitter handles to make them more visible and user-friendly"""
        # Apply stylesheet to make handles more visible
        handle_style = f"""
            QSplitter::handle {{
                background-color: {const.BUTTON_BORDER};
                border-radius: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {const.BUTTON_BORDER};
            }}
            QSplitter::handle:pressed {{
                background-color: {const.BUTTON_BORDER};
            }}
        """
        self.splitter.setStyleSheet(handle_style)

        # Set cursor to horizontal resize cursor for better UX
        for i in range(self.splitter.count() - 1):  # For each handle
            handle = self.splitter.handle(i + 1)
            handle.setCursor(QtCore.Qt.SplitHCursor)

    def _setup_floating_panel(self):
        """Set up the floating panel for additional controls.

        Creates a floating panel with content panel inside it. The floating panel
        provides access to editing functionality that can be expanded or collapsed.
        Connects signals and positions the panel in the UI.
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.components.content_panel import ContentPanel
        from cgame_avatar_factory.face_sculpting_center.ui.components.floating_panel import FloatingPanel

        self.floating_panel = FloatingPanel(self.dna_merge_container)

        self.content_panel = ContentPanel(self)

        self.floating_panel.set_content_widget(self.content_panel)

        self.floating_panel.sig_expanded_changed.connect(self._on_floating_panel_state_changed)

        def _place_floating_panel():
            if not self.dna_merge_container or not self.floating_panel:
                return
            margin = 65
            x = 5
            y = max(0, self.dna_merge_container.height() - self.floating_panel.height() - margin)
            self.floating_panel.move(x, y)

        QtCore.QTimer.singleShot(200, _place_floating_panel)

    def reset_dna_ring(self):
        """Clear DNA components from the ring without recreating the ring widget.

        Removes all DNA components from the ring and reinitializes the pivot.
        This is used when resetting the state without destroying the entire widget.

        Returns:
            bool: True if the operation was successful, False otherwise
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.utils import collapse_utils

        try:
            self.logger.info("清除环中的DNA组件")

            if not hasattr(self, "ring") or not self.ring:
                self.logger.error("环组件不存在，无法清除DNA")
                return False

            self.ring.clear_components()
            collapse_utils.delete_mesh_history()
            if hasattr(self, "pivot_move") and self.pivot_move:
                self.pivot_move.init_pivot()

            self.logger.info("DNA环中的组件已清除")
            collapse_utils.delete_nodes()
            return True

        except Exception as e:
            self.logger.error("清除DNA环组件失败: %s", str(e))
            return False

    def setup_dna_lib(self):
        """Set up the DNA library widget.

        Creates and configures the DNA library widget, which provides access to
        available DNA components that can be used for merging. Sets the widget to
        be frameless and adds it to the operation layout.
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.dna_lib.dna_lib_widgets import DNALibWidget

        self.dna_lib = DNALibWidget()
        self.dna_lib.frameless()
        self.dna_lib.setMinimumSize(500, 300)
        self.splitter.addWidget(self.dna_lib)

    def remove_dna_lib(self):
        """Remove the DNA library widget from the layout.

        Removes the DNA library widget from the operation layout without destroying it.
        This is typically used when reconfiguring the UI or when the DNA library is
        temporarily not needed.
        """
        self.operation_layout.removeWidget(self.dna_lib)

    def setup_functional_area(self, parent_layout):
        """Set up the functional area of the UI.

        Creates a container for functional controls including top and bottom functional areas
        and random/reset controls. The functional area is initially disabled and will be
        enabled when appropriate conditions are met.

        Args:
            parent_layout: The parent layout to add the functional area to
        """
        self.functional_area_container = QtWidgets.QFrame()
        self.functional_area_layout = FramelessVLayout()
        self.functional_area_container.setLayout(self.functional_area_layout)
        self.functional_area_container.setEnabled(False)
        self.functional_area_container.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)

        separator = QtWidgets.QFrame(self)
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.functional_area_layout.addWidget(separator)

        parent_layout.addWidget(self.functional_area_container)

    def reset_current_area_dna(self, current_areas):
        self.scene_weight_manager.reset_merge(current_areas)

        if hasattr(self, "pivot_move") and self.pivot_move:
            weights = [1.0, 0.0, 0.0, 0.0]
            self.pivot_move.set_pos_by_weight(weights, block_signal=True)

    def _on_multi_select_changed(self, checked):
        """Handle changes to the multi-select checkbox state.

        When multi-select is enabled, allows multiple area buttons to be selected simultaneously.
        When disabled, enforces exclusive selection by unchecking all but the first selected button.

        Args:
            checked: Boolean indicating whether multi-select is enabled (True) or disabled (False)
        """
        if hasattr(self, "area_select_widget"):
            self.area_select_widget.set_exclusive(not checked)

            if not checked:
                checked_buttons = [btn for btn in self.area_select_widget.button_group.buttons() if btn.isChecked()]
                if len(checked_buttons) > 1:
                    for button in checked_buttons[1:]:
                        button.setChecked(False)
                        button.setStyleSheet("")

                    selected_areas = [checked_buttons[0].text()]
                    self.area_select_widget.selected_buttons_changed.emit(selected_areas)

    def setup_status_bar(self, parent_layout):
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.ui.components.status_bar import ProgressWidget

        self.status_bar = ProgressWidget()
        parent_layout.addWidget(self.status_bar)

    def get_merge_state(self):
        """Get current state of merge UI for debugging."""
        state = {
            "components_count": len(self.ring.components),
            "component_positions": self.ring.get_component_positions(),
            "point_position": self.ring.point_pos,
            "weights": self.pivot_move.weights if self.pivot_move else None,
        }
        return state

    def get_current_areas(self):
        return self.area_select_widget.current_areas()

    def resizeEvent(self, event):
        super(BaseMergePage, self).resizeEvent(event)

        if hasattr(self, "floating_panel") and self.floating_panel:
            self.floating_panel.updatePosition()

        if hasattr(self, "random_and_reset_area_container"):
            parent_width = self.functional_area_container.width()
            self.random_and_reset_area_container.setFixedWidth(parent_width // 3)

    def _on_face_symmetry_changed(self, state):
        """Handle changes to face symmetry state.

        Updates the face customization symmetry setting based on checkbox state.
        When enabled, changes to one side of the face are mirrored to the other side.
        When disabled, each side can be edited independently.

        Args:
            state: The current state of the symmetry checkbox (True for checked, False for unchecked)
        """
        current_selection = cmds.ls(selection=True)
        symmetrical = customization.Iosymmetrical()
        if state:
            symmetrical.set_symmetrical_true()
            customization.symmetry_mode()

        else:
            symmetrical.set_symmetrical_false()
            customization.remove_selection_changed_script_jobs()
        if current_selection:
            cmds.select(current_selection, replace=True)

    @QtCore.Slot(dict)
    def slot_dna_added_to_ring(self, dna_data):
        """Handle DNA added to the ring.

        Processes a DNA that has been added to the DNA ring component,
        logging the event and triggering DNA settings changes.

        Args:
            dna_data: Dictionary containing information about the added DNA
        """
        self.logger.debug("DNA added to ring, dna_data: {}".format(dna_data))

        dna_path = dna_data["dna_file_path"]
        self.logger.debug(dna_path)
        self.slot_dna_settings_changed()
        self.save_scene_state()
        ScenStateManager.save()

    @QtCore.Slot(dict)
    def slot_dna_removed_from_ring(self, dna_data):
        """Handle DNA removed from the ring.

        Processes a DNA that has been removed from the DNA ring component,
        logging the event for tracking purposes.

        Args:
            dna_data: Dictionary containing information about the removed DNA
        """
        # Import local modules
        import cgame_avatar_factory.face_sculpting_center.utils.collapse_utils as collapse_utils

        self.logger.debug("DNA removed from ring, dna_data: {}".format(dna_data))
        self._dna_components = self.ring.components
        if len(self._dna_components) == const.DEFAULT_DNA_NUM - 1:
            collapse_utils.delete_mesh_blendshape()
            collapse_utils.delete_nodes()
            self.ring.setAcceptDrops(True)
            self.disable_face_tabs()
        self.save_scene_state()
        ScenStateManager.save()

    @QtCore.Slot(list)
    def slot_merge_area_select_changed(self, values):
        """Handle changes to the merge area selection.

        Updates the merge range based on selected areas and adjusts the pivot position
        based on normalized weights for the selected area.

        Args:
            values: List of selected merge area values
        """
        self.logger.debug("Merge area select changed, values: %s", values)
        if self.scene_weight_manager:
            self.scene_weight_manager.update_range(values)
        # Only auto-adjust pivot when a single area is selected.
        # For multiple selections, pivot adjustment is ambiguous and thus skipped.
        if len(values):
            weights = self.scene_weight_manager.get_weights_from_scene()[values[0]]
            total = sum(weights)
            if total > 0:
                normalized_weights = [w / total for w in weights]
                self.pivot_move.set_pos_by_weight(normalized_weights, block_signal=True)

    @QtCore.Slot(dict)
    def slot_dna_clicked_in_ring(self, dna_data):
        """Handle DNA clicked in the ring.

        Processes a click event on a DNA in the ring component,
        logging the event for tracking purposes.

        Args:
            dna_data: Dictionary containing information about the clicked DNA
        """
        self.logger.debug("DNA clicked in ring, dna_data: {}".format(dna_data))

    @QtCore.Slot(dict)
    def slot_dna_changed_in_ring(self, dna_data):
        """Handle DNA changed in the ring.

        Processes changes to a DNA in the ring component, updating the component
        with new data and triggering DNA settings changes. Prevents changes to base DNA.

        Args:
            dna_data: Dictionary containing information about the changed DNA
        """
        order = dna_data.get("dna_order")
        if order == 0:
            dayu_widgets.MToast.warning("基础DNA\n不允许更改！", parent=self)
            return
        component = self.ring.components[order]
        component.update_dna_data(dna_data)
        self.slot_dna_settings_changed()

    @QtCore.Slot()
    def slot_progress_bar_clicked(self):
        """Handle progress bar click.

        Toggles the visibility of the log widget when the progress bar is clicked,
        allowing users to view or hide application logs.
        """
        # if self.log_widget.isVisible():
        #    self.log_widget.hide()
        # else:
        #    self.log_widget.show()
        #    self.log_widget.raise_()

    @QtCore.Slot()
    def slot_dna_settings_changed(self):
        """Handle changes to DNA settings.

        Updates the DNA list from the ring components and checks if all DNAs have valid
        file paths. Finishes DNA settings if all required DNAs are present, otherwise resets settings.
        """
        self._dna_components = self.ring.components
        if all([dna.dna_file_path for dna in self._dna_components]):
            if len(self._dna_components) == 1:
                first_dna = self._dna_components[0]
                is_collapsed_mode = first_dna.get_collapsed_model()
                self.logger.info(
                    f"first_dna: {first_dna.dna_file_path}, is_collapsed_mode: {is_collapsed_mode}",
                )

                if not is_collapsed_mode:
                    self.check_single_dna_animation_timer = QtCore.QTimer()
                    self.check_single_dna_animation_timer.setInterval(10)
                    self.check_single_dna_animation_timer.timeout.connect(
                        self.check_single_dna_animation,
                    )
                    self.check_single_dna_animation_timer.start()

                    if hasattr(self, "floating_panel") and self.floating_panel:
                        self.floating_panel.toggle_button.setEnabled(
                            True,
                        )
                        self.floating_panel.toggle_button.setToolTip(
                            "切换编辑模式",
                        )
                        self.floating_panel.toggle_button.setCursor(
                            QtCore.Qt.PointingHandCursor,
                        )

            elif len(self._dna_components) == const.DEFAULT_DNA_NUM:
                self.slot_finished_dna_settings()
                self.enable_face_tabs()
        else:
            self.slot_reset_dna_settings()

    @QtCore.Slot()
    def slot_finished_dna_settings(self):
        """Handle completion of DNA settings.

        Disables drag and drop for the DNA ring, sets merge data, enables functional areas,
        and starts a timer to check animation status for building the mesh.
        """
        self.logger.debug("finished dna settings")
        self.ring.setAcceptDrops(False)
        self.slot_set_merge_data()
        self.slot_enable_functional_area()

        self.check_animation_timer = QtCore.QTimer()
        self.check_animation_timer.setInterval(10)
        self.check_animation_timer.timeout.connect(self._check_animation_status)
        self.check_animation_timer.start()

    def _check_animation_status(self):
        """Check the status of DNA ring animations.

        Monitors whether the DNA ring is ready for overlay operations and
        starts a background thread to execute mesh building when ready.
        """
        if self.ring.is_ready_for_overlay():
            self.check_animation_timer.stop()
            self._execute_build_mesh()

    def _execute_build_mesh(self):
        """Execute the mesh building operation.

        Builds the mesh using the current DNA file paths, disables loading indication,
        initializes the scene call, and sets default widget values.
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.build_face.customization_rig_builder import (
            CustomizationRigBuilder,
        )
        from cgame_avatar_factory.face_sculpting_center.build_face.customization_rig_builder import (
            get_build_signal_manager,
        )
        from cgame_avatar_factory.face_sculpting_center.merge.merge_data import get_merge_data_context

        signal_manager = get_build_signal_manager()
        signal_manager.progress_signal.connect(self.update_build_progress)

        # try:
        # Get collapsed mode from first DNA if available
        is_collapsed_mode = False
        if self._dna_components and len(self._dna_components) > 0:
            is_collapsed_mode = self._dna_components[0].get_collapsed_model()

        builder = CustomizationRigBuilder(
            dna_paths=get_merge_data_context().dna_file_paths,
            collapsed_mode=is_collapsed_mode,
        )
        builder.build()
        self.logger.info("Build completed successfully")

        # except Exception as e:
        #     self.logger.error(f"Build mesh failed: {e}")
        #     dayu_widgets.MToast.error(f"Build mesh failed: {str(e)}", self)
        # finally:
        #     #self.loading_wrapper.set_dayu_loading(False)
        #     signal_manager.progress_signal.disconnect(self.update_build_progress)

        self.set_widgets_default_value()

    def update_build_progress(self, progress):
        """更新BuildMesh进度条

        Args:
            progress (dict): 包含进度信息的字典，格式为 {"value": int, "text": str}
                value: 进度值，范围0-100
                text: 进度文本描述
        """
        self.logger.debug("更新BuildMesh进度: {}".format(progress))
        if hasattr(
            self,
            "status_bar",
        ):
            self.status_bar.progress_bar.setValue(
                progress.get("value", 0),
            )
            if "text" in progress:
                self.status_bar.success_label.setText(
                    progress.get("text", ""),
                )

    @QtCore.Slot()
    def slot_set_merge_data(self):
        """Set the merge data for DNA blending.

        Initializes area weights with default values (1.0 for first DNA, 0.0 for others)
        and updates the merge data context with current area weights and DNA information.
        """
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.merge.merge_data import get_merge_data_context

        dna_infos = []
        for dna in self._dna_components:
            filtered_dna_data = {
                "dna_file_path": dna.dna_data.get("dna_file_path"),
                "thumbnail_file_path": dna.dna_data.get("thumbnail_file_path"),
                "dna_order": dna.dna_data.get("dna_order"),
            }
            dna_infos.append(filtered_dna_data)

        get_merge_data_context().update_dna_infos(dna_infos)

        self.logger.debug("dna_infos: {}".format(get_merge_data_context().dna_infos))

    @QtCore.Slot()
    def check_single_dna_animation(self):
        first_dna_widget = self.ring.components[0]
        animation = getattr(first_dna_widget, "animation", None)
        if (animation is None) or (animation.state() == QAbstractAnimation.Stopped):
            self.check_single_dna_animation_timer.stop()
            self.process_single_dna()

    def _update_progress(self, value, text):
        """更新进度条和状态文本

        将重复的状态栏更新逻辑抽取为独立方法，减少代码重复

        Args:
            value (int): 进度条值（0-100）
            text (str): 状态文本
        """
        if hasattr(
            self,
            "status_bar",
        ):
            self.status_bar.progress_bar.setValue(value)
            self.status_bar.success_label.setText(text)

    @QtCore.Slot()
    def process_single_dna(self):
        """Process single DNA file"""
        # Import local modules
        from cgame_avatar_factory.face_sculpting_center.build_face.single_dna_builder import SingleDNABuilder

        try:
            builder = SingleDNABuilder(progress_callback=self._update_progress, base_merge_page=self)
            builder.build(self._dna_components[0].dna_file_path)
            self.save_scene_state()
        except Exception as e:
            self.logger.error(f"Process single DNA failed: {e}")
            dayu_widgets.MToast.error(f"Process single DNA failed: {str(e)}", self)

    @QtCore.Slot()
    def slot_reset_dna_settings(self):
        """Reset DNA settings.

        Disables the functional area container and logs the reset action.
        """
        self.functional_area_container.setEnabled(False)
        self.logger.debug("reset dna settings")

    @QtCore.Slot()
    def set_widgets_default_value(self):
        """Set default values for UI widgets.

        Sets the default area selection, enables mirror merge mode, and sets the default weight values.
        """
        self.logger.debug("set widgets default value")
        self.slot_set_area_select_menu_default_value()

        self.pivot_move.set_pos_by_weight([1.0, 0.0, 0.0, 0.0])

    @QtCore.Slot()
    def slot_set_area_select_menu_default_value(self):
        """Set default value for area selection menu.

        Sets the area selection to 'all' by default.
        """
        self.area_select_widget.select_all()

    @QtCore.Slot()
    def slot_set_preview_animation_select_menu_default_value(self):
        """Set default value for preview animation selection menu.

        Sets the animation selection to the first animation in the configuration.
        """
        animation_list = list(self.anim_config.keys())
        self.preview_animation_select_menu.set_value(animation_list[0])

    @QtCore.Slot()
    def slot_enable_functional_area(self):
        """Enable the functional area container.

        Enables user interaction with the functional area and logs the action.
        """
        self.functional_area_container.setEnabled(True)
        self.logger.debug("enabled functional area")

    @QtCore.Slot()
    def slot_disable_functional_area(self):
        """Disable the functional area container.

        Disables user interaction with the functional area and logs the action.
        """
        self.functional_area_container.setEnabled(False)
        self.logger.debug("disabled functional area")

    def hideEvent(self, event):
        """
        当页面被隐藏时触发（例如从 QStackedWidget 切走本页）。
        在此调用 hide_all_ctrl。
        """
        super().hideEvent(event)
        try:
            customization.hide_all_ctrl(face_const.FACE_CTRL_BASIC_GRP, face_const.FACE_CTRL_DETAILED_GRP)
        except Exception as e:
            self.logger.warning(f"Failed to hide controllers on hideEvent: {e}")
