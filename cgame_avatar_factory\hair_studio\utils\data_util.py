# Import built-in modules
import uuid
from functools import wraps
import time
import logging
from typing import Callable, Any, Optional, Dict
from threading import Lock

# Import third-party modules
from qtpy import QtCore


def generate_short_unique_name(prefix=""):
    return f"{prefix}_{str(uuid.uuid4())[:8]}"


def lazy_load(method):
    """
    装饰tab的数据加载方法。首次激活加载一次，之后不再重复加载。
    自动给组件加 is_loaded 属性。
    """
    def wrapper(self, *args, **kwargs):
        if not hasattr(self, 'is_loaded'):
            self.is_loaded = False
        if not self.is_loaded:
            self.is_loaded = True
            return method(self, *args, **kwargs)
        # 如果已加载直接跳过
    return wrapper


def analyze_time(func):
    """分析函数运行时间的装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        logging.debug(f"[Timeit] {func.__qualname__} 耗时: {(end - start)*1000:.2f} ms")
        return result
    return wrapper


def async_preload(
    signal_prefix: str = "async_loading",
    auto_start: bool = False
):
    """
    异步预加载装饰器。
    
    为类方法添加异步预加载功能，自动创建工作线程和信号管理。
    
    Args:
        signal_prefix: 信号名前缀，会自动创建 {prefix}_started, {prefix}_finished, {prefix}_failed 信号
        auto_start: 是否在类初始化时自动开始异步加载
    
    Usage:
        @async_preload(signal_prefix="assets_loading")
        def load_assets(self):
            # 耗时的加载操作
            pass
    """
    def decorator(method):
        method_name = method.__name__
        
        # 标记这个方法为异步预加载方法
        method._is_async_preload = True
        method._signal_prefix = signal_prefix
        method._auto_start = auto_start
        
        @wraps(method)
        def wrapper(self, *args, **kwargs):
            # 正常执行原方法（同步版本）
            return method(self, *args, **kwargs)
        
        # 保持装饰器信息
        wrapper._is_async_preload = True
        wrapper._signal_prefix = signal_prefix
        wrapper._auto_start = auto_start
        wrapper._original_method = method
        
        return wrapper
    return decorator


class AsyncLoadWorker(QtCore.QThread):
    """通用异步加载工作线程."""
    
    # 通用信号
    loading_started = QtCore.Signal(str)      # method_name
    loading_finished = QtCore.Signal(str, object)  # method_name, result
    loading_failed = QtCore.Signal(str, str)  # method_name, error_message
    loading_progress = QtCore.Signal(str, int, str)  # method_name, progress, message
    
    def __init__(self, target_object, method_name: str, method: Callable, args=None, kwargs=None, parent=None):
        super(AsyncLoadWorker, self).__init__(parent)
        self._target_object = target_object
        self._method_name = method_name
        self._method = method
        self._args = args or ()
        self._kwargs = kwargs or {}
        self._logger = logging.getLogger(__name__)
        
    def run(self):
        """执行异步加载任务."""
        try:
            self._logger.info(f"Starting async loading: {self._method_name}")
            start_time = QtCore.QTime.currentTime()
            
            # 发送开始信号
            self.loading_started.emit(self._method_name)
            
            # 执行目标方法
            result = self._method(self._target_object, *self._args, **self._kwargs)
            
            elapsed = start_time.msecsTo(QtCore.QTime.currentTime())
            self._logger.info(f"Async loading completed: {self._method_name} in {elapsed}ms")
            
            # 发送完成信号
            self.loading_finished.emit(self._method_name, result)
            
        except Exception as e:
            error_msg = f"Async loading failed for {self._method_name}: {e}"
            self._logger.error(error_msg, exc_info=True)
            self.loading_failed.emit(self._method_name, error_msg)
    
    def report_progress(self, progress: int, message: str = ""):
        """报告加载进度."""
        self.loading_progress.emit(self._method_name, progress, message)


class AsyncLoadManager(QtCore.QObject):
    """异步加载管理器."""
    
    def __init__(self, target_object, parent=None):
        super(AsyncLoadManager, self).__init__(parent)
        self._target_object = target_object
        self._workers: Dict[str, AsyncLoadWorker] = {}
        self._methods: Dict[str, Dict] = {}  # method_name -> {method, signal_prefix, status}
        self._lock = Lock()
        self._logger = logging.getLogger(__name__)
        
        # 自动发现并注册异步预加载方法
        self._discover_async_methods()
    
    def _discover_async_methods(self):
        """自动发现目标对象中的异步预加载方法."""
        for attr_name in dir(self._target_object):
            attr = getattr(self._target_object, attr_name)
            if hasattr(attr, '_is_async_preload') and attr._is_async_preload:
                # 获取原始方法（未装饰的版本）
                original_method = getattr(attr, '_original_method', attr)
                signal_prefix = getattr(attr, '_signal_prefix', 'async_loading')
                auto_start = getattr(attr, '_auto_start', False)
                
                self.register_async_method(
                    method_name=attr_name,
                    method=original_method,
                    signal_prefix=signal_prefix
                )
                
                self._logger.debug(f"Discovered async method: {attr_name} with prefix: {signal_prefix}")
    
    def register_async_method(self, method_name: str, method: Callable, signal_prefix: str = "async_loading"):
        """注册异步方法."""
        with self._lock:
            self._methods[method_name] = {
                'method': method,
                'signal_prefix': signal_prefix,
                'status': 'registered',  # registered, loading, loaded, failed
                'result': None,
                'error': None
            }
            
        self._logger.debug(f"Registered async method: {method_name}")
    
    def start_async_loading(self, method_name: str, *args, **kwargs):
        """启动异步加载."""
        if method_name not in self._methods:
            self._logger.error(f"Method {method_name} not registered")
            return False
            
        method_info = self._methods[method_name]
        
        # 检查状态
        if method_info['status'] == self.LOADING_STATUS:
            self._logger.debug(f"Method {method_name} already loading")
            return False
            
        if method_info['status'] == self.LOADED_STATUS:
            self._logger.debug(f"Method {method_name} already loaded")
            return False
        
        # 创建工作线程
        worker = AsyncLoadWorker(
            target_object=self._target_object,
            method_name=method_name,
            method=method_info['method'],
            args=args,
            kwargs=kwargs,
            parent=self
        )
        
        # 连接信号
        signal_prefix = method_info['signal_prefix']
        worker.loading_started.connect(lambda mn: self._on_loading_started(mn, signal_prefix))
        worker.loading_finished.connect(lambda mn, result: self._on_loading_finished(mn, result, signal_prefix))
        worker.loading_failed.connect(lambda mn, error: self._on_loading_failed(mn, error, signal_prefix))
        worker.loading_progress.connect(lambda mn, progress, msg: self._on_loading_progress(mn, progress, msg, signal_prefix))
        worker.finished.connect(lambda: self._cleanup_worker(method_name))
        
        # 保存worker引用
        self._workers[method_name] = worker
        method_info['status'] = self.LOADING_STATUS
        
        # 启动线程
        worker.start()
        self._logger.info(f"Started async loading for: {method_name}")
        return True

    # 在类的开头或其他适当位置添加以下常量定义（需要在实际类定义中添加）
    LOADING_STATUS = 'loading'
    LOADED_STATUS = 'loaded'
    
    def _on_loading_started(self, method_name: str, signal_prefix: str):
        """处理加载开始."""
        signal_name = f"{signal_prefix}_started"
        if hasattr(self._target_object, signal_name):
            signal = getattr(self._target_object, signal_name)
            if hasattr(signal, 'emit'):
                signal.emit()
                self._logger.debug(f"Emitted signal: {signal_name}")
    
    def _on_loading_finished(self, method_name: str, result: Any, signal_prefix: str):
        """处理加载完成."""
        if method_name in self._methods:
            self._methods[method_name]['status'] = 'loaded'
            self._methods[method_name]['result'] = result
            
        signal_name = f"{signal_prefix}_finished"
        if hasattr(self._target_object, signal_name):
            signal = getattr(self._target_object, signal_name)
            if hasattr(signal, 'emit'):
                signal.emit(result)
                self._logger.debug(f"Emitted signal: {signal_name}")
    
    def _on_loading_failed(self, method_name: str, error: str, signal_prefix: str):
        """处理加载失败."""
        if method_name in self._methods:
            self._methods[method_name]['status'] = 'failed'
            self._methods[method_name]['error'] = error
            
        signal_name = f"{signal_prefix}_failed"
        if hasattr(self._target_object, signal_name):
            signal = getattr(self._target_object, signal_name)
            if hasattr(signal, 'emit'):
                signal.emit(error)
                self._logger.debug(f"Emitted signal: {signal_name}")
    
    def _on_loading_progress(self, method_name: str, progress: int, message: str, signal_prefix: str):
        """处理加载进度."""
        signal_name = f"{signal_prefix}_progress"
        if hasattr(self._target_object, signal_name):
            signal = getattr(self._target_object, signal_name)
            if hasattr(signal, 'emit'):
                signal.emit(progress, message)
                self._logger.debug(f"Emitted signal: {signal_name}")
    
    def _cleanup_worker(self, method_name: str):
        """清理工作线程."""
        if method_name in self._workers:
            worker = self._workers[method_name]
            # 先从字典中移除，避免重复清理
            del self._workers[method_name]
            
            # 确保线程完全结束后再删除
            if worker.isRunning():
                worker.quit()
                worker.wait(5000)  # 等待最多5秒
            
            worker.deleteLater()
    
    def cleanup_all_workers(self):
        """清理所有工作线程."""
        for method_name in list(self._workers.keys()):
            self._cleanup_worker(method_name)
    
    def is_loading(self, method_name: str) -> bool:
        """检查是否正在加载."""
        return (method_name in self._methods and 
                self._methods[method_name]['status'] == 'loading')
    
    def is_loaded(self, method_name: str) -> bool:
        """检查是否已加载."""
        return (method_name in self._methods and 
                self._methods[method_name]['status'] == 'loaded')
    
    def get_result(self, method_name: str) -> Any:
        """获取加载结果."""
        if method_name in self._methods:
            return self._methods[method_name].get('result')
        return None
    
    def get_status(self, method_name: str) -> str:
        """获取加载状态."""
        if method_name in self._methods:
            return self._methods[method_name]['status']
        return 'unknown'