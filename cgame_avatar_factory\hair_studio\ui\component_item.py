"""Component item widget for the Hair Studio component list.

This module provides a custom widget for displaying hair components in the component list
with icons, visibility toggle, and other visual elements.
"""

# Import built-in modules
import logging

# Import third-party modules
from dayu_widgets import <PERSON><PERSON>abe<PERSON>
from dayu_widgets import <PERSON><PERSON><PERSON><PERSON>on
from qtpy import QtCore
from qtpy import QtGui
from qtpy import QtWidgets
from qtpy.QtCore import Qt
from qtpy.QtCore import Signal

# Import local modules
from cgame_avatar_factory.hair_studio.constants import BUTTON_MIN_ICON_SIZE
from cgame_avatar_factory.hair_studio.constants import BUTTON_MIN_SIZE
from cgame_avatar_factory.hair_studio.constants import COMPONENT_ITEM_HEIGHT
from cgame_avatar_factory.hair_studio.constants import ICON_EYE_LINE
from cgame_avatar_factory.hair_studio.constants import ICON_EYE_OFF_LINE
from cgame_avatar_factory.hair_studio.constants import LAYOUT_MARGIN_SMALL
from cgame_avatar_factory.hair_studio.constants import LAYOUT_SPACING_SMALL
from cgame_avatar_factory.hair_studio.constants import BUTTON_ICON_PADDING_RATIO  # Button constants
from cgame_avatar_factory.hair_studio.constants import COMPONENT_ITEM_STYLE_NORMAL
from cgame_avatar_factory.hair_studio.constants import COMPONENT_ITEM_STYLE_SELECTED
from cgame_avatar_factory.hair_studio.constants import VISIBILITY_BUTTON_FALLBACK_HIDDEN
from cgame_avatar_factory.hair_studio.constants import VISIBILITY_BUTTON_FALLBACK_VISIBLE


class ResponsiveSquareButton(MPushButton):
    """A responsive square button that maintains aspect ratio and scales icons dynamically."""

    def __init__(self, parent=None):
        super(ResponsiveSquareButton, self).__init__(parent)
        self._icon_name = None
        self._icon_padding_ratio = BUTTON_ICON_PADDING_RATIO
        self._logger = logging.getLogger(__name__)

        # Set size policy to allow flexible sizing
        self.setSizePolicy(
            QtWidgets.QSizePolicy.Preferred,
            QtWidgets.QSizePolicy.Preferred,
        )

    def sizeHint(self):
        """Return the preferred size hint - square based on available height."""
        # Get the parent's available height and use it as both width and height
        parent_height = COMPONENT_ITEM_HEIGHT - (2 * LAYOUT_MARGIN_SMALL)
        return QtCore.QSize(parent_height, parent_height)

    def minimumSizeHint(self):
        """Return minimum size hint."""
        return QtCore.QSize(BUTTON_MIN_SIZE, BUTTON_MIN_SIZE)

    def resizeEvent(self, event):
        """Handle resize events to maintain square shape and update icon size."""
        super(ResponsiveSquareButton, self).resizeEvent(event)

        # Ensure square shape
        size = event.size()
        square_size = min(size.width(), size.height())

        if size.width() != square_size or size.height() != square_size:
            # Resize to square if not already square
            self.resize(square_size, square_size)
            return

        # Update icon size based on new button size
        self._update_icon_size()

    def _update_icon_size(self):
        """Update icon size based on current button size."""
        if not self._icon_name:
            return

        button_size = min(self.width(), self.height())
        if button_size <= 0:
            return

        # Calculate icon size with padding
        icon_size = int(button_size * (1 - self._icon_padding_ratio))
        icon_size = max(icon_size, BUTTON_MIN_ICON_SIZE)

        # Set the icon size
        self.setIconSize(QtCore.QSize(icon_size, icon_size))

    def set_icon_with_fallback(self, icon_name):
        """Set icon with fallback support and automatic sizing."""
        self._icon_name = icon_name

        # Use the existing icon utility but let our resizeEvent handle sizing
        try:
            # Import local modules
            from cgame_avatar_factory.hair_studio.utils.icon_utils import get_icon_with_fallback

            icon = get_icon_with_fallback(icon_name, None)
            if icon and not icon.isNull():
                self.setIcon(icon)
                self._update_icon_size()
        except Exception as e:
            self._logger.error("Failed to set icon %s: %s", icon_name, e)

    def heightForWidth(self, width):
        """Return height for given width to maintain square aspect ratio."""
        return width

    def hasHeightForWidth(self):
        """Indicate that this widget has a height-for-width preference."""
        return True


class ComponentItem(QtWidgets.QWidget):
    """Custom widget for displaying a hair component in the list.

    This widget shows the component name, asset icon, and visibility toggle.
    """

    # Signals
    clicked = Signal(dict)  # Emitted when the component is clicked
    visibility_toggled = Signal(
        str,
        bool,
    )  # Emitted when visibility is toggled (component_id, is_visible)

    def __init__(self, component_data, parent=None):
        """Initialize the ComponentItem.

        Args:
            component_data (dict): The component data
            parent (QWidget, optional): The parent widget
        """
        super(ComponentItem, self).__init__(parent)
        self.component_data = component_data
        # Always use this module's own logger to maintain module identity
        self._logger = logging.getLogger(__name__)

        # Track selection state
        self._is_selected = False

        # Set up UI
        self.setup_ui()

        # Set initial state
        self._update_visibility_button()
        self._update_style()

    def setup_ui(self):
        """Set up the user interface."""
        # Main horizontal layout
        layout = QtWidgets.QHBoxLayout(self)
        layout.setContentsMargins(
            LAYOUT_MARGIN_SMALL,
            LAYOUT_MARGIN_SMALL,
            LAYOUT_MARGIN_SMALL,
            LAYOUT_MARGIN_SMALL,
        )
        layout.setSpacing(LAYOUT_SPACING_SMALL)

        # 1. Visibility toggle button (eye icon) - FIRST
        self.visibility_button = ResponsiveSquareButton()
        # No fixed size - let the button adapt to available space
        # The ResponsiveSquareButton will maintain square aspect ratio automatically
        self.visibility_button.clicked.connect(self._on_visibility_clicked)
        self._update_visibility_button()

        # 3. Component name label - THIRD
        component_name = self.component_data.get("name", "Unnamed Component")
        self.name_label = MLabel(component_name)
        self.name_label.set_elide_mode(Qt.ElideRight)
        self.name_label.setToolTip(self.name_label.text())
        self.name_label.setAlignment(Qt.AlignVCenter | Qt.AlignLeft)

        # Enable mouse events for name label to make it clickable
        self.name_label.setAttribute(Qt.WA_Hover, True)

        # Add widgets to layout in correct order: eye -> asset icon -> name
        layout.addWidget(self.visibility_button)
        layout.addWidget(self.name_label, 1)  # Stretch to fill available space

        # Set size policy and height with strong constraints for MListView indexWidget
        self.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        self.setFixedHeight(COMPONENT_ITEM_HEIGHT)
        self.setMinimumHeight(COMPONENT_ITEM_HEIGHT)
        self.setMaximumHeight(COMPONENT_ITEM_HEIGHT)

        # Install event filters on child widgets to capture their mouse events
        self._setup_event_filters()

        # Debug: Measure actual button dimensions (remove in production)
        if self._logger.isEnabledFor(logging.DEBUG):
            QtCore.QTimer.singleShot(100, self._debug_button_measurements)

    def _update_visibility_button(self):
        """Update the visibility button icon based on component state."""
        is_visible = self.component_data.get("is_viewed", True)

        try:
            if is_visible:
                self.visibility_button.set_icon_with_fallback(ICON_EYE_LINE)
                self.visibility_button.setToolTip("Hide component")
                self.visibility_button.setText("")  # Clear any text
            else:
                self.visibility_button.set_icon_with_fallback(ICON_EYE_OFF_LINE)
                self.visibility_button.setToolTip("Show component")
                self.visibility_button.setText("")  # Clear any text

        except Exception as e:
            self._logger.warning("Failed to set visibility icon: %s", str(e))
            # Fallback to text-based button
            if is_visible:
                self.visibility_button.setText(VISIBILITY_BUTTON_FALLBACK_VISIBLE)
                self.visibility_button.setToolTip("Hide component")
            else:
                self.visibility_button.setText(VISIBILITY_BUTTON_FALLBACK_HIDDEN)
                self.visibility_button.setToolTip("Show component")

    def _setup_event_filters(self):
        """Setup event filters on child widgets to capture mouse events."""

        # Install event filter on name label to capture clicks
        self.name_label.installEventFilter(self)

        # Note: We don't install filter on visibility_button as it needs to handle its own clicks

    def _on_visibility_clicked(self):
        """Handle visibility button click.

        This method handles visibility toggle. With incremental updates, we can
        maintain a cleaner separation between selection and visibility operations.
        """
        current_visibility = self.component_data.get("is_viewed", True)
        new_visibility = not current_visibility

        # IMPORTANT: Ensure this component is selected when visibility is toggled
        # This provides consistent UX - any interaction with a component should select it
        self.clicked.emit(self.component_data)

        # Emit visibility signal - the manager will handle the actual state change
        # and send back an incremental update that won't disrupt the selection
        component_id = self.component_data.get("id")
        if component_id:
            self.visibility_toggled.emit(component_id, new_visibility)

        component_name = self.component_data.get("name", "Unknown")
        visibility_state = "visible" if new_visibility else "hidden"
        self._logger.debug(
            "Component visibility toggle requested: %s -> %s (component selected)",
            component_name,
            visibility_state,
        )

    def eventFilter(self, obj, event):
        """Event filter to capture mouse events from child widgets."""
        # Handle mouse press events on child widgets (name_label)
        if event.type() == QtCore.QEvent.MouseButtonPress:
            if event.button() == Qt.LeftButton:
                # Only handle clicks on name_label, not visibility_button
                if obj is self.name_label:
                    self._logger.debug(
                        "Mouse click captured from child widget: %s",
                        obj.__class__.__name__,
                    )
                    # Emit clicked signal for component selection
                    self.clicked.emit(self.component_data)
                    return True  # Event handled, don't propagate further
            elif event.button() == Qt.RightButton:
                # Handle right-click events for context menu
                if obj is self.name_label:
                    self._logger.debug(
                        "Right-click captured from child widget: %s",
                        obj.__class__.__name__,
                    )
                    # Create a context menu event and send it to ourselves
                    context_event = QtGui.QContextMenuEvent(
                        QtGui.QContextMenuEvent.Mouse,
                        event.pos(),
                        obj.mapToGlobal(event.pos()),
                    )
                    self.contextMenuEvent(context_event)
                    return True  # Event handled, don't propagate further

        # Handle hover events for visual feedback
        elif event.type() == QtCore.QEvent.Enter:
            if obj is self.name_label:
                self._set_hover_state(True)
        elif event.type() == QtCore.QEvent.Leave:
            if obj is self.name_label:
                self._set_hover_state(False)

        # Let the event continue to the original widget
        return super(ComponentItem, self).eventFilter(obj, event)

    def _debug_button_measurements(self):
        """Debug method to measure actual button dimensions."""
        try:
            button = self.visibility_button

            # Get actual sizes
            actual_size = button.size()
            actual_icon_size = button.iconSize()
            actual_pos = button.pos()

            # Get parent container info
            container_size = self.size()
            available_height = COMPONENT_ITEM_HEIGHT - (2 * LAYOUT_MARGIN_SMALL)

            

            # Check if button fits within available space
            overflow = actual_size.height() - available_height
            # Check icon scaling
            button_size = min(actual_size.width(), actual_size.height())
            expected_icon_size = int(button_size * 0.8)  # 80% of button size
            # Check vertical centering
            expected_y = (container_size.height() - actual_size.height()) // 2
            y_offset = actual_pos.y() - expected_y
            

        except Exception as e:
            self._logger.error("Error in button debug measurements: %s", e)

    def mousePressEvent(self, event):
        """Handle mouse press events on the ComponentItem itself."""
        if event.button() == Qt.LeftButton:
            self._logger.debug("Direct click on ComponentItem background")
            self.clicked.emit(self.component_data)
        super(ComponentItem, self).mousePressEvent(event)

    def contextMenuEvent(self, event):
        """Handle context menu events for the ComponentItem.

        This method ensures that right-click events anywhere on the ComponentItem
        (including child widgets) are properly handled by the context menu system.

        Note: Context menu only appears if this item is currently selected.
        """
        self._logger.debug("Context menu event on ComponentItem")

        # Check if this item is selected - only show context menu for selected items
        if not self.is_selected():
            self._logger.debug("Context menu blocked: item is not selected")
            event.accept()  # Accept the event to prevent further propagation
            return

        # Check if we have a context handler installed
        if hasattr(self, "_context_handler") and self._context_handler:
            # Use the installed context handler
            handled = self._context_handler.handle_component_context_menu(
                self,
                event.pos(),
            )
            if handled:
                event.accept()
                return

        # If no context handler or handler couldn't process, try to propagate to parent
        # This ensures compatibility with the existing MListView context menu system
        parent_widget = self.parent()
        if parent_widget:
            # Map the position to parent coordinates
            parent_pos = parent_widget.mapFromGlobal(event.globalPos())

            # Create a new context menu event for the parent
            parent_event = QtGui.QContextMenuEvent(
                event.reason(),
                parent_pos,
                event.globalPos(),
            )

            # Send the event to the parent
            QtWidgets.QApplication.sendEvent(parent_widget, parent_event)
            event.accept()
        else:
            # No parent to propagate to, just accept the event
            event.accept()

    def enterEvent(self, event):
        """Handle mouse enter events for hover effects."""
        self._set_hover_state(True)
        super(ComponentItem, self).enterEvent(event)

    def leaveEvent(self, event):
        """Handle mouse leave events for hover effects."""
        self._set_hover_state(False)
        super(ComponentItem, self).leaveEvent(event)

    def _set_hover_state(self, is_hovering):
        """Set hover state for visual feedback.

        Args:
            is_hovering (bool): True if mouse is hovering, False otherwise
        """
        # This can be used to add hover effects in the future
        # For now, we'll keep the existing style system
        # The parameter is kept for future implementation
        _ = is_hovering  # Suppress unused parameter warning

    def sizeHint(self):
        """Return the preferred size for this widget.

        This is crucial for MListView indexWidget to respect the component height.
        """
        return QtCore.QSize(-1, COMPONENT_ITEM_HEIGHT)

    def minimumSizeHint(self):
        """Return the minimum size for this widget.

        Ensures MListView doesn't compress the widget below the intended height.
        """
        return QtCore.QSize(-1, COMPONENT_ITEM_HEIGHT)

    def set_selected(self, selected):
        """Set the selection state.

        Args:
            selected (bool): True to select, False to deselect
        """
        if self._is_selected != selected:
            self._is_selected = selected
            self._update_style()

    def is_selected(self):
        """Check if this item is selected.

        Returns:
            bool: True if selected, False otherwise
        """
        return self._is_selected

    def _update_style(self):
        """Update the widget style based on selection state."""
        if self._is_selected:
            self.setStyleSheet(COMPONENT_ITEM_STYLE_SELECTED)
        else:
            self.setStyleSheet(COMPONENT_ITEM_STYLE_NORMAL)

    def get_component_data(self):
        """Get the component data.

        Returns:
            dict: The component data
        """
        return self.component_data

    def update_component_data(self, component_data):
        """Update the component data and refresh the display.

        Args:
            component_data (dict): The new component data
        """
        self.component_data = component_data

        # Update display elements
        component_name = component_data.get("name", "Unnamed Component")
        self.name_label.setText(component_name)
        self.name_label.setToolTip(self.name_label.text())
        self._update_visibility_button()
